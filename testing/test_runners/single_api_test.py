#!/usr/bin/env python3
"""
Single API test runner.

Tests individual APIs with visualization and detailed reporting.
"""

import argparse
import sys
import os
from pathlib import Path
import json
import time
from typing import Dict, Any, Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from api_clients import (
    MainProcessingClient,
    SegmentAutoCropClient, 
    SegmentCorePiecesClient,
    SegmentCoreServerClient
)
from visualization.image_visualizer import ImageVisualizer


class SingleAPITester:
    """Test runner for individual API testing."""
    
    def __init__(self):
        self.visualizer = ImageVisualizer()
        self.clients = {
            'main_processing': MainProcessingClient(),
            'segment_auto_crop': SegmentAutoCropClient(),
            'segment_core_pieces': SegmentCorePiecesClient(),
            'segment_core_server': SegmentCoreServerClient()
        }
    
    def test_api(
        self,
        api_name: str,
        image_path: str,
        visualize: bool = True,
        save_results: bool = True,
        output_dir: str = "results"
    ) -> Dict[str, Any]:
        """
        Test a single API with an image.
        
        Args:
            api_name: Name of the API to test
            image_path: Path to the test image
            visualize: Whether to show visualizations
            save_results: Whether to save results to files
            output_dir: Directory to save results
            
        Returns:
            Test results dictionary
        """
        if api_name not in self.clients:
            raise ValueError(f"Unknown API: {api_name}. Available: {list(self.clients.keys())}")
        
        client = self.clients[api_name]
        
        # Check API health
        print(f"🔍 Testing {api_name} API...")
        if not client.health_check():
            print(f"❌ {api_name} API is not responding")
            return {'success': False, 'error': 'API not responding'}
        
        print(f"✅ {api_name} API is healthy")
        
        # Load original image
        try:
            original_image = self.visualizer.load_image(image_path)
            print(f"📷 Loaded image: {image_path} ({original_image.shape})")
        except Exception as e:
            print(f"❌ Failed to load image: {e}")
            return {'success': False, 'error': f'Failed to load image: {e}'}
        
        # Run API test
        try:
            start_time = time.time()
            
            if api_name == 'main_processing':
                result = client.process_image(image_path, use_segmentation=True)
            elif api_name == 'segment_auto_crop':
                result = client.segment_crop(image_path)
            elif api_name == 'segment_core_pieces':
                result = client.segment_core_pieces(image_path)
            elif api_name == 'segment_core_server':
                result = client.process_core_outline(image_path, use_row=True)
            
            processing_time = time.time() - start_time
            print(f"⏱️  Processing completed in {processing_time:.2f} seconds")
            
        except Exception as e:
            print(f"❌ API request failed: {e}")
            return {'success': False, 'error': f'API request failed: {e}'}
        
        # Analyze results
        analysis = self._analyze_result(api_name, result)
        print(f"📊 Analysis: {analysis}")
        
        # Save results if requested
        if save_results:
            self._save_results(api_name, image_path, result, analysis, output_dir)
        
        # Visualize if requested
        if visualize:
            self._visualize_result(api_name, original_image, result, output_dir if save_results else None)
        
        return {
            'success': True,
            'api_name': api_name,
            'processing_time': processing_time,
            'result': result,
            'analysis': analysis
        }
    
    def _analyze_result(self, api_name: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze API result and extract key metrics."""
        analysis = {
            'api_name': api_name,
            'response_size': len(str(result)),
            'has_metadata': '_metadata' in result
        }
        
        if api_name == 'main_processing':
            analysis.update({
                'has_warped_image': 'warped_image' in result,
                'google_results_count': len(result.get('google_result', [])),
                'gemini_results_count': len(result.get('gemini_result', [])),
                'coordinate_arrays_length': len(result.get('x', []))
            })
        
        elif api_name in ['segment_auto_crop', 'segment_core_pieces']:
            key = 'detection' if api_name == 'segment_auto_crop' else 'segmentation'
            predictions = result.get(key, {}).get('predictions', [])
            analysis.update({
                'predictions_count': len(predictions),
                'has_warped_image_url': 'warped_image_url' in result,
                'class_distribution': self._get_class_distribution(predictions)
            })
        
        elif api_name == 'segment_core_server':
            blocks = result.get('blocks', [])
            analysis.update({
                'blocks_count': len(blocks),
                'blocks_with_google_text': sum(1 for b in blocks if b.get('google_text')),
                'blocks_with_gemini_text': sum(1 for b in blocks if b.get('gemini_text')),
                'has_warped_image_url': 'warped_image_url' in result
            })
        
        return analysis
    
    def _get_class_distribution(self, predictions: list) -> Dict[str, int]:
        """Get distribution of classes in predictions."""
        distribution = {}
        for pred in predictions:
            class_name = pred.get('class', 'Unknown')
            distribution[class_name] = distribution.get(class_name, 0) + 1
        return distribution
    
    def _save_results(
        self,
        api_name: str,
        image_path: str,
        result: Dict[str, Any],
        analysis: Dict[str, Any],
        output_dir: str
    ) -> None:
        """Save test results to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save JSON result
        timestamp = int(time.time())
        result_file = output_path / f"{api_name}_{timestamp}_result.json"
        with open(result_file, 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        # Save analysis
        analysis_file = output_path / f"{api_name}_{timestamp}_analysis.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {result_file}")
        print(f"💾 Analysis saved to: {analysis_file}")
    
    def _visualize_result(
        self,
        api_name: str,
        original_image,
        result: Dict[str, Any],
        save_dir: Optional[str] = None
    ) -> None:
        """Visualize API results."""
        print(f"🎨 Creating visualizations for {api_name}...")
        
        save_path = None
        if save_dir:
            timestamp = int(time.time())
            save_path = f"{save_dir}/{api_name}_{timestamp}_visualization.png"
        
        if api_name == 'main_processing':
            # Show original with coordinates if available
            if 'x' in result and 'y' in result:
                segments = []
                for i in range(len(result['x'])):
                    segments.append({
                        'x': result['x'][i],
                        'y': result['y'][i], 
                        'width': result['width'][i],
                        'height': result['height'][i],
                        'class': 'Block'
                    })
                
                self.visualizer.display_image_with_segments(
                    original_image, segments,
                    title=f"Main Processing Results ({len(segments)} blocks)",
                    save_path=save_path
                )
            
            # Show warped image if available
            if 'warped_image' in result:
                try:
                    warped_image = self.visualizer.decode_base64_image(result['warped_image'])
                    self.visualizer.display_before_after(
                        original_image, warped_image,
                        title="Main Processing: Before and After",
                        save_path=save_path.replace('.png', '_comparison.png') if save_path else None
                    )
                except Exception as e:
                    print(f"⚠️  Could not decode warped image: {e}")
        
        elif api_name in ['segment_auto_crop', 'segment_core_pieces']:
            key = 'detection' if api_name == 'segment_auto_crop' else 'segmentation'
            predictions = result.get(key, {}).get('predictions', [])
            
            if predictions:
                self.visualizer.display_image_with_segments(
                    original_image, predictions,
                    title=f"{api_name.replace('_', ' ').title()} Results ({len(predictions)} segments)",
                    save_path=save_path
                )
        
        elif api_name == 'segment_core_server':
            blocks = result.get('blocks', [])
            
            if blocks:
                self.visualizer.display_image_with_text_blocks(
                    original_image, blocks,
                    title=f"Segment Core Server Results ({len(blocks)} blocks)",
                    save_path=save_path
                )


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Test individual aibase-ml APIs')
    parser.add_argument('--api', required=True, 
                       choices=['main_processing', 'segment_auto_crop', 'segment_core_pieces', 'segment_core_server'],
                       help='API to test')
    parser.add_argument('--image', required=True, help='Path to test image')
    parser.add_argument('--no-visualize', action='store_true', help='Skip visualizations')
    parser.add_argument('--no-save', action='store_true', help='Skip saving results')
    parser.add_argument('--output-dir', default='results', help='Output directory for results')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    
    args = parser.parse_args()
    
    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Check if image exists
    if not Path(args.image).exists():
        print(f"❌ Image file not found: {args.image}")
        sys.exit(1)
    
    # Run test
    tester = SingleAPITester()
    
    try:
        result = tester.test_api(
            api_name=args.api,
            image_path=args.image,
            visualize=not args.no_visualize,
            save_results=not args.no_save,
            output_dir=args.output_dir
        )
        
        if result['success']:
            print(f"✅ Test completed successfully!")
            print(f"⏱️  Processing time: {result['processing_time']:.2f} seconds")
        else:
            print(f"❌ Test failed: {result['error']}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
