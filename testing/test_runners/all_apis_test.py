#!/usr/bin/env python3
"""
All APIs test runner.

Tests all 4 APIs with the same image and provides comparison visualizations.
"""

import argparse
import sys
import os
from pathlib import Path
import json
import time
from typing import Dict, Any, List
import matplotlib.pyplot as plt

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from api_clients import (
    MainProcessingClient,
    SegmentAutoCropClient, 
    SegmentCorePiecesClient,
    SegmentCoreServerClient
)
from visualization.image_visualizer import ImageVisualizer


class AllAPIsTester:
    """Test runner for testing all APIs with comparison."""
    
    def __init__(self):
        self.visualizer = ImageVisualizer()
        self.clients = {
            'main_processing': MainProcessingClient(),
            'segment_auto_crop': SegmentAutoCropClient(),
            'segment_core_pieces': SegmentCorePiecesClient(),
            'segment_core_server': SegmentCoreServerClient()
        }
    
    def test_all_apis(
        self,
        image_path: str,
        visualize: bool = True,
        save_results: bool = True,
        output_dir: str = "results"
    ) -> Dict[str, Any]:
        """
        Test all APIs with the same image.
        
        Args:
            image_path: Path to the test image
            visualize: Whether to show visualizations
            save_results: Whether to save results to files
            output_dir: Directory to save results
            
        Returns:
            Combined test results dictionary
        """
        print(f"🚀 Testing all APIs with image: {image_path}")
        
        # Load original image
        try:
            original_image = self.visualizer.load_image(image_path)
            print(f"📷 Loaded image: {image_path} ({original_image.shape})")
        except Exception as e:
            print(f"❌ Failed to load image: {e}")
            return {'success': False, 'error': f'Failed to load image: {e}'}
        
        # Test each API
        results = {}
        total_time = 0
        
        for api_name, client in self.clients.items():
            print(f"\n🔍 Testing {api_name}...")
            
            # Check API health
            if not client.health_check():
                print(f"❌ {api_name} API is not responding")
                results[api_name] = {'success': False, 'error': 'API not responding'}
                continue
            
            print(f"✅ {api_name} API is healthy")
            
            # Run API test
            try:
                start_time = time.time()
                
                if api_name == 'main_processing':
                    result = client.process_image(image_path, use_segmentation=True)
                elif api_name == 'segment_auto_crop':
                    result = client.segment_crop(image_path)
                elif api_name == 'segment_core_pieces':
                    result = client.segment_core_pieces(image_path)
                elif api_name == 'segment_core_server':
                    result = client.process_core_outline(image_path, use_row=True)
                
                processing_time = time.time() - start_time
                total_time += processing_time
                
                print(f"⏱️  {api_name} completed in {processing_time:.2f} seconds")
                
                # Analyze result
                analysis = self._analyze_result(api_name, result)
                
                results[api_name] = {
                    'success': True,
                    'processing_time': processing_time,
                    'result': result,
                    'analysis': analysis
                }
                
            except Exception as e:
                print(f"❌ {api_name} failed: {e}")
                results[api_name] = {'success': False, 'error': str(e)}
        
        # Create comparison summary
        comparison = self._create_comparison(results)
        print(f"\n📊 Total processing time: {total_time:.2f} seconds")
        print(f"📊 Successful APIs: {comparison['successful_apis']}/{comparison['total_apis']}")
        
        # Save results if requested
        if save_results:
            self._save_all_results(image_path, results, comparison, output_dir)
        
        # Visualize if requested
        if visualize:
            self._visualize_all_results(original_image, results, output_dir if save_results else None)
        
        return {
            'success': True,
            'image_path': image_path,
            'total_processing_time': total_time,
            'results': results,
            'comparison': comparison
        }
    
    def _analyze_result(self, api_name: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze individual API result."""
        analysis = {
            'api_name': api_name,
            'response_size': len(str(result)),
            'has_metadata': '_metadata' in result
        }
        
        if api_name == 'main_processing':
            analysis.update({
                'has_warped_image': 'warped_image' in result,
                'google_results_count': len(result.get('google_result', [])),
                'gemini_results_count': len(result.get('gemini_result', [])),
                'coordinate_arrays_length': len(result.get('x', []))
            })
        
        elif api_name in ['segment_auto_crop', 'segment_core_pieces']:
            key = 'detection' if api_name == 'segment_auto_crop' else 'segmentation'
            predictions = result.get(key, {}).get('predictions', [])
            analysis.update({
                'predictions_count': len(predictions),
                'has_warped_image_url': 'warped_image_url' in result,
                'class_distribution': self._get_class_distribution(predictions)
            })
        
        elif api_name == 'segment_core_server':
            blocks = result.get('blocks', [])
            analysis.update({
                'blocks_count': len(blocks),
                'blocks_with_google_text': sum(1 for b in blocks if b.get('google_text')),
                'blocks_with_gemini_text': sum(1 for b in blocks if b.get('gemini_text')),
                'has_warped_image_url': 'warped_image_url' in result
            })
        
        return analysis
    
    def _get_class_distribution(self, predictions: list) -> Dict[str, int]:
        """Get distribution of classes in predictions."""
        distribution = {}
        for pred in predictions:
            class_name = pred.get('class', 'Unknown')
            distribution[class_name] = distribution.get(class_name, 0) + 1
        return distribution
    
    def _create_comparison(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Create comparison summary across all APIs."""
        successful_apis = sum(1 for r in results.values() if r.get('success', False))
        total_apis = len(results)
        
        processing_times = {
            api: r.get('processing_time', 0) 
            for api, r in results.items() 
            if r.get('success', False)
        }
        
        # Compare detection/segmentation counts
        detection_counts = {}
        for api, result in results.items():
            if not result.get('success', False):
                continue
                
            analysis = result.get('analysis', {})
            if 'predictions_count' in analysis:
                detection_counts[api] = analysis['predictions_count']
            elif 'blocks_count' in analysis:
                detection_counts[api] = analysis['blocks_count']
            elif 'coordinate_arrays_length' in analysis:
                detection_counts[api] = analysis['coordinate_arrays_length']
        
        return {
            'total_apis': total_apis,
            'successful_apis': successful_apis,
            'success_rate': successful_apis / total_apis if total_apis > 0 else 0,
            'processing_times': processing_times,
            'fastest_api': min(processing_times.items(), key=lambda x: x[1])[0] if processing_times else None,
            'slowest_api': max(processing_times.items(), key=lambda x: x[1])[0] if processing_times else None,
            'detection_counts': detection_counts,
            'apis_with_text_processing': [
                api for api, r in results.items() 
                if r.get('success') and (
                    'google_results_count' in r.get('analysis', {}) or
                    'blocks_with_google_text' in r.get('analysis', {})
                )
            ]
        }
    
    def _save_all_results(
        self,
        image_path: str,
        results: Dict[str, Any],
        comparison: Dict[str, Any],
        output_dir: str
    ) -> None:
        """Save all test results to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        
        # Save combined results
        combined_file = output_path / f"all_apis_{timestamp}_results.json"
        with open(combined_file, 'w') as f:
            json.dump({
                'image_path': image_path,
                'timestamp': timestamp,
                'results': results,
                'comparison': comparison
            }, f, indent=2, default=str)
        
        # Save comparison summary
        summary_file = output_path / f"all_apis_{timestamp}_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(comparison, f, indent=2, default=str)
        
        print(f"💾 Combined results saved to: {combined_file}")
        print(f"💾 Summary saved to: {summary_file}")
    
    def _visualize_all_results(
        self,
        original_image,
        results: Dict[str, Any],
        save_dir: str = None
    ) -> None:
        """Create comprehensive visualization of all API results."""
        print(f"🎨 Creating comprehensive visualizations...")
        
        successful_results = {api: r for api, r in results.items() if r.get('success', False)}
        
        if not successful_results:
            print("⚠️  No successful results to visualize")
            return
        
        # Create comparison plot
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        axes = axes.flatten()
        
        for i, (api_name, result_data) in enumerate(successful_results.items()):
            if i >= 4:
                break
                
            ax = axes[i]
            ax.imshow(original_image)
            ax.set_title(f"{api_name.replace('_', ' ').title()}", fontsize=14, fontweight='bold')
            ax.axis('off')
            
            result = result_data['result']
            
            # Add API-specific annotations
            if api_name == 'main_processing' and 'x' in result:
                for j in range(len(result['x'])):
                    x, y, w, h = result['x'][j], result['y'][j], result['width'][j], result['height'][j]
                    rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='red', facecolor='none')
                    ax.add_patch(rect)
            
            elif api_name in ['segment_auto_crop', 'segment_core_pieces']:
                key = 'detection' if api_name == 'segment_auto_crop' else 'segmentation'
                predictions = result.get(key, {}).get('predictions', [])
                
                for j, pred in enumerate(predictions[:10]):  # Limit to first 10
                    color = self.visualizer.colors[j % len(self.visualizer.colors)]
                    x, y, w, h = pred.get('x', 0), pred.get('y', 0), pred.get('width', 0), pred.get('height', 0)
                    rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor=color, facecolor='none')
                    ax.add_patch(rect)
            
            elif api_name == 'segment_core_server':
                blocks = result.get('blocks', [])
                for j, block in enumerate(blocks[:10]):  # Limit to first 10
                    color = self.visualizer.colors[j % len(self.visualizer.colors)]
                    x, y, w, h = block.get('x', 0), block.get('y', 0), block.get('width', 0), block.get('height', 0)
                    rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor=color, facecolor='none')
                    ax.add_patch(rect)
        
        # Hide unused subplots
        for i in range(len(successful_results), 4):
            axes[i].axis('off')
        
        plt.suptitle("All APIs Comparison", fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        if save_dir:
            timestamp = int(time.time())
            save_path = f"{save_dir}/all_apis_{timestamp}_comparison.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"💾 Comparison visualization saved to: {save_path}")
        
        plt.show()
        
        # Create performance comparison chart
        self._create_performance_chart(successful_results, save_dir)
    
    def _create_performance_chart(self, results: Dict[str, Any], save_dir: str = None) -> None:
        """Create performance comparison chart."""
        api_names = list(results.keys())
        processing_times = [results[api]['processing_time'] for api in api_names]
        
        plt.figure(figsize=(12, 6))
        bars = plt.bar(api_names, processing_times, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        
        plt.title('API Processing Time Comparison', fontsize=16, fontweight='bold')
        plt.ylabel('Processing Time (seconds)', fontsize=12)
        plt.xlabel('API', fontsize=12)
        
        # Add value labels on bars
        for bar, time_val in zip(bars, processing_times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
        
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        if save_dir:
            timestamp = int(time.time())
            save_path = f"{save_dir}/all_apis_{timestamp}_performance.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"💾 Performance chart saved to: {save_path}")
        
        plt.show()


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Test all aibase-ml APIs')
    parser.add_argument('--image', required=True, help='Path to test image')
    parser.add_argument('--no-visualize', action='store_true', help='Skip visualizations')
    parser.add_argument('--no-save', action='store_true', help='Skip saving results')
    parser.add_argument('--output-dir', default='results', help='Output directory for results')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    
    args = parser.parse_args()
    
    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Check if image exists
    if not Path(args.image).exists():
        print(f"❌ Image file not found: {args.image}")
        sys.exit(1)
    
    # Run test
    tester = AllAPIsTester()
    
    try:
        result = tester.test_all_apis(
            image_path=args.image,
            visualize=not args.no_visualize,
            save_results=not args.no_save,
            output_dir=args.output_dir
        )
        
        if result['success']:
            print(f"\n✅ All APIs test completed!")
            print(f"⏱️  Total processing time: {result['total_processing_time']:.2f} seconds")
            print(f"📊 Success rate: {result['comparison']['success_rate']:.1%}")
        else:
            print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
