# API Testing Suite - aibase-ml

This directory contains a comprehensive testing suite for manually testing and visualizing the results of all 4 APIs in the aibase-ml application.

## Directory Structure

```
testing/
├── README.md                    # This file
├── sample_images/              # Test images for API testing
│   ├── core_sample_1.jpg      # Sample core drilling image
│   ├── core_sample_2.jpg      # Another core sample
│   ├── box_sample.jpg         # Image with clear box boundaries
│   └── mixed_sample.jpg       # Complex image with multiple elements
├── api_clients/               # API client functions
│   ├── __init__.py
│   ├── main_processing_client.py
│   ├── segment_auto_crop_client.py
│   ├── segment_core_pieces_client.py
│   └── segment_core_server_client.py
├── visualization/             # Visualization utilities
│   ├── __init__.py
│   ├── image_visualizer.py    # Image display and annotation
│   ├── result_visualizer.py   # API result visualization
│   └── comparison_visualizer.py # Compare results across APIs
├── test_runners/              # Test execution scripts
│   ├── __init__.py
│   ├── single_api_test.py     # Test individual API
│   ├── all_apis_test.py       # Test all APIs with same image
│   └── batch_test.py          # Batch test multiple images
└── results/                   # Test results and outputs
    ├── images/                # Processed images
    ├── responses/             # JSON responses
    └── reports/               # Test reports
```

## Quick Start

### 1. Setup Environment

```bash
# Install dependencies
pip install requests matplotlib opencv-python pillow numpy pandas

# Ensure APIs are running
python apis/main_processing.py &      # Port 8386
python apis/segment_auto_crop.py &    # Port 8388
python apis/segment_core_pieces.py &  # Port 8381
python apis/segment_core_server.py &  # Port 8389
```

### 2. Run Single API Test

```bash
cd testing
python test_runners/single_api_test.py --api main_processing --image sample_images/core_sample_1.jpg
```

### 3. Run All APIs Test

```bash
python test_runners/all_apis_test.py --image sample_images/core_sample_1.jpg --visualize
```

### 4. Run Batch Test

```bash
python test_runners/batch_test.py --input_dir sample_images/ --output_dir results/
```

## API Client Usage

### Main Processing API

```python
from api_clients.main_processing_client import MainProcessingClient

client = MainProcessingClient(base_url="http://localhost:8386")

# Test with segmentation
result = client.process_image(
    image_path="sample_images/core_sample_1.jpg",
    use_segmentation=True,
    depth_from=0.0,
    depth_to=2.4
)

# Test with custom prompt
result = client.process_image(
    image_path="sample_images/core_sample_1.jpg",
    custom_prompt="Extract depth measurements from this core sample"
)
```

### Segment Auto Crop API

```python
from api_clients.segment_auto_crop_client import SegmentAutoCropClient

client = SegmentAutoCropClient(base_url="http://localhost:8388")
result = client.segment_crop(image_path="sample_images/core_sample_1.jpg")
```

### Segment Core Pieces API

```python
from api_clients.segment_core_pieces_client import SegmentCorePiecesClient

client = SegmentCorePiecesClient(base_url="http://localhost:8381")
result = client.segment_core_pieces(image_path="sample_images/core_sample_1.jpg")
```

### Segment Core Server API

```python
from api_clients.segment_core_server_client import SegmentCoreServerClient

client = SegmentCoreServerClient(base_url="http://localhost:8389")

# Test with row transformation
result = client.process_core_outline(
    image_path="sample_images/core_sample_1.jpg",
    use_row=True,
    depth_from=0.0,
    depth_to=2.4
)

# Test with custom prompt
result = client.process_core_outline(
    image_path="sample_images/core_sample_1.jpg",
    custom_prompt="Identify geological features in this core sample"
)
```

## Visualization Features

### Image Visualization

```python
from visualization.image_visualizer import ImageVisualizer

visualizer = ImageVisualizer()

# Display original image with segments
visualizer.display_image_with_segments(
    image_path="sample_images/core_sample_1.jpg",
    segments=result['detection']['predictions']
)

# Display before/after comparison
visualizer.display_before_after(
    original_image="sample_images/core_sample_1.jpg",
    processed_image=result['warped_image']
)
```

### Result Visualization

```python
from visualization.result_visualizer import ResultVisualizer

visualizer = ResultVisualizer()

# Visualize API response
visualizer.visualize_api_response(
    api_name="main_processing",
    response=result,
    save_path="results/images/main_processing_result.png"
)

# Create summary report
visualizer.create_summary_report(
    results=all_api_results,
    output_path="results/reports/summary.html"
)
```

### Comparison Visualization

```python
from visualization.comparison_visualizer import ComparisonVisualizer

visualizer = ComparisonVisualizer()

# Compare results from all APIs
visualizer.compare_all_apis(
    image_path="sample_images/core_sample_1.jpg",
    results={
        "main_processing": main_result,
        "segment_auto_crop": crop_result,
        "segment_core_pieces": pieces_result,
        "segment_core_server": server_result
    }
)
```

## Test Scenarios

### 1. Basic Functionality Test
- Test each API with a simple core sample image
- Verify response format and required fields
- Check for successful processing without errors

### 2. Parameter Variation Test
- Test different depth ranges (0-1, 1-2, 0-3)
- Test with and without segmentation flags
- Test custom prompts vs default processing

### 3. Image Quality Test
- Test with high-resolution images
- Test with low-resolution images
- Test with different aspect ratios

### 4. Error Handling Test
- Test with invalid image formats
- Test with corrupted images
- Test with missing parameters

### 5. Performance Test
- Measure response times for each API
- Test with batch processing
- Monitor memory usage during processing

## Expected Results

### Main Processing API
- **Response Time**: 5-15 seconds
- **Key Fields**: `warped_image`, `google_result`, `gemini_result`, `x`, `y`, `width`, `height`
- **Visualization**: Original image, warped image, segments with OCR/AI text

### Segment Auto Crop API
- **Response Time**: 3-8 seconds
- **Key Fields**: `detection.predictions`, `warped_image_url`
- **Visualization**: Original image, warped image, aligned segments

### Segment Core Pieces API
- **Response Time**: 3-8 seconds
- **Key Fields**: `segmentation.predictions`, `warped_image_url`
- **Visualization**: Original image, warped image, core pieces as rows

### Segment Core Server API
- **Response Time**: 8-20 seconds
- **Key Fields**: `blocks` with `google_text` and `gemini_text`
- **Visualization**: Original image, blocks with text annotations

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure APIs are running on correct ports
2. **Timeout Errors**: Increase timeout for large images or slow processing
3. **Memory Errors**: Use smaller images or increase system memory
4. **Azure Upload Failures**: Check Azure credentials and connection

### Debug Mode

Enable debug mode for detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run tests with debug output
python test_runners/single_api_test.py --debug --api main_processing --image sample_images/core_sample_1.jpg
```

### Performance Monitoring

Monitor API performance:

```python
from test_runners.performance_monitor import PerformanceMonitor

monitor = PerformanceMonitor()
results = monitor.benchmark_all_apis(
    image_path="sample_images/core_sample_1.jpg",
    iterations=5
)
```

## Contributing

When adding new test scenarios:

1. Add sample images to `sample_images/` directory
2. Create corresponding test cases in `test_runners/`
3. Update visualization functions if needed
4. Document expected results and performance benchmarks
5. Update this README with new test scenarios
