"""
Image visualization utilities for API testing.

Provides functions to display images with annotations, segments, and processing results.
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon
import base64
from io import BytesIO
from PIL import Image
from typing import List, Dict, Any, Optional, Tuple
import json


class ImageVisualizer:
    """Utility class for visualizing images and API results."""
    
    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        """
        Initialize the visualizer.
        
        Args:
            figsize: Default figure size for matplotlib plots
        """
        self.figsize = figsize
        self.colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ]
    
    def load_image(self, image_path: str) -> np.ndarray:
        """
        Load an image from file path.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Image as numpy array in RGB format
        """
        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"Could not load image: {image_path}")
        return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    def decode_base64_image(self, base64_string: str) -> np.ndarray:
        """
        Decode a base64 encoded image.
        
        Args:
            base64_string: Base64 encoded image string
            
        Returns:
            Image as numpy array in RGB format
        """
        image_data = base64.b64decode(base64_string)
        image = Image.open(BytesIO(image_data))
        return np.array(image.convert('RGB'))
    
    def display_image_with_segments(
        self,
        image: np.ndarray,
        segments: List[Dict[str, Any]],
        title: str = "Image with Segments",
        show_confidence: bool = True,
        show_class: bool = True,
        save_path: Optional[str] = None
    ) -> None:
        """
        Display an image with segment annotations.
        
        Args:
            image: Image as numpy array
            segments: List of segment dictionaries with x, y, width, height
            title: Plot title
            show_confidence: Whether to show confidence scores
            show_class: Whether to show class labels
            save_path: Optional path to save the plot
        """
        fig, ax = plt.subplots(1, 1, figsize=self.figsize)
        ax.imshow(image)
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        
        for i, segment in enumerate(segments):
            color = self.colors[i % len(self.colors)]
            
            # Extract coordinates
            x = segment.get('x', 0)
            y = segment.get('y', 0)
            width = segment.get('width', 0)
            height = segment.get('height', 0)
            
            # Draw bounding box
            rect = patches.Rectangle(
                (x, y), width, height,
                linewidth=2, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            
            # Add label
            label_parts = []
            if show_class and 'class' in segment:
                label_parts.append(segment['class'])
            if show_confidence and 'confidence' in segment:
                label_parts.append(f"{segment['confidence']:.2f}")
            
            if label_parts:
                label = ' - '.join(label_parts)
                ax.text(
                    x, y - 5, label,
                    fontsize=10, color=color, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8)
                )
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved visualization to: {save_path}")
        
        plt.show()
    
    def display_image_with_points(
        self,
        image: np.ndarray,
        segments: List[Dict[str, Any]],
        title: str = "Image with Point Annotations",
        save_path: Optional[str] = None
    ) -> None:
        """
        Display an image with polygon point annotations.
        
        Args:
            image: Image as numpy array
            segments: List of segment dictionaries with points
            title: Plot title
            save_path: Optional path to save the plot
        """
        fig, ax = plt.subplots(1, 1, figsize=self.figsize)
        ax.imshow(image)
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        
        for i, segment in enumerate(segments):
            color = self.colors[i % len(self.colors)]
            
            # Extract points
            points = segment.get('points', [])
            if not points:
                continue
            
            # Convert points to polygon
            if len(points) >= 6:  # At least 3 points (x,y pairs)
                polygon_points = [(points[j], points[j+1]) for j in range(0, len(points), 2)]
                polygon = Polygon(polygon_points, closed=True, fill=False, edgecolor=color, linewidth=2)
                ax.add_patch(polygon)
                
                # Add class label
                if 'class' in segment:
                    centroid_x = sum(p[0] for p in polygon_points) / len(polygon_points)
                    centroid_y = sum(p[1] for p in polygon_points) / len(polygon_points)
                    ax.text(
                        centroid_x, centroid_y, segment['class'],
                        fontsize=10, color=color, fontweight='bold',
                        ha='center', va='center',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8)
                    )
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved visualization to: {save_path}")
        
        plt.show()
    
    def display_before_after(
        self,
        original_image: np.ndarray,
        processed_image: np.ndarray,
        title: str = "Before and After Processing",
        save_path: Optional[str] = None
    ) -> None:
        """
        Display original and processed images side by side.
        
        Args:
            original_image: Original image as numpy array
            processed_image: Processed image as numpy array
            title: Plot title
            save_path: Optional path to save the plot
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        ax1.imshow(original_image)
        ax1.set_title("Original Image", fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        ax2.imshow(processed_image)
        ax2.set_title("Processed Image", fontsize=14, fontweight='bold')
        ax2.axis('off')
        
        fig.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved comparison to: {save_path}")
        
        plt.show()
    
    def display_image_with_text_blocks(
        self,
        image: np.ndarray,
        blocks: List[Dict[str, Any]],
        title: str = "Image with Text Blocks",
        show_google_text: bool = True,
        show_gemini_text: bool = True,
        save_path: Optional[str] = None
    ) -> None:
        """
        Display an image with text block annotations.
        
        Args:
            image: Image as numpy array
            blocks: List of block dictionaries with text results
            title: Plot title
            show_google_text: Whether to show Google OCR text
            show_gemini_text: Whether to show Gemini AI text
            save_path: Optional path to save the plot
        """
        fig, ax = plt.subplots(1, 1, figsize=self.figsize)
        ax.imshow(image)
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.axis('off')
        
        for i, block in enumerate(blocks):
            color = self.colors[i % len(self.colors)]
            
            # Extract coordinates
            x = block.get('x', 0)
            y = block.get('y', 0)
            width = block.get('width', 0)
            height = block.get('height', 0)
            
            # Draw bounding box
            rect = patches.Rectangle(
                (x, y), width, height,
                linewidth=2, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            
            # Prepare text annotations
            text_lines = []
            
            if show_google_text and block.get('google_text'):
                google_text = block['google_text'][:50] + "..." if len(block['google_text']) > 50 else block['google_text']
                text_lines.append(f"OCR: {google_text}")
            
            if show_gemini_text and block.get('gemini_text'):
                gemini_text = block['gemini_text'][:50] + "..." if len(block['gemini_text']) > 50 else block['gemini_text']
                text_lines.append(f"AI: {gemini_text}")
            
            # Add text annotation
            if text_lines:
                text = '\n'.join(text_lines)
                ax.text(
                    x, y - 10, text,
                    fontsize=8, color=color, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9),
                    verticalalignment='top'
                )
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved text block visualization to: {save_path}")
        
        plt.show()
    
    def create_processing_summary(
        self,
        original_image: np.ndarray,
        processed_image: Optional[np.ndarray],
        segments: List[Dict[str, Any]],
        processing_info: Dict[str, Any],
        save_path: Optional[str] = None
    ) -> None:
        """
        Create a comprehensive processing summary visualization.
        
        Args:
            original_image: Original image
            processed_image: Processed/warped image (optional)
            segments: List of detected segments
            processing_info: Processing metadata and statistics
            save_path: Optional path to save the plot
        """
        if processed_image is not None:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        else:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # Original image
        ax1.imshow(original_image)
        ax1.set_title("Original Image", fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # Original with segments
        ax2.imshow(original_image)
        for i, segment in enumerate(segments):
            color = self.colors[i % len(self.colors)]
            x, y, w, h = segment.get('x', 0), segment.get('y', 0), segment.get('width', 0), segment.get('height', 0)
            rect = patches.Rectangle((x, y), w, h, linewidth=2, edgecolor=color, facecolor='none')
            ax2.add_patch(rect)
        ax2.set_title(f"Detected Segments ({len(segments)})", fontsize=14, fontweight='bold')
        ax2.axis('off')
        
        if processed_image is not None:
            # Processed image
            ax3.imshow(processed_image)
            ax3.set_title("Processed/Warped Image", fontsize=14, fontweight='bold')
            ax3.axis('off')
            
            # Processing info
            ax4.axis('off')
            info_text = []
            for key, value in processing_info.items():
                if isinstance(value, float):
                    info_text.append(f"{key}: {value:.2f}")
                else:
                    info_text.append(f"{key}: {value}")
            
            ax4.text(0.1, 0.9, '\n'.join(info_text), transform=ax4.transAxes,
                    fontsize=12, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))
            ax4.set_title("Processing Information", fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved processing summary to: {save_path}")
        
        plt.show()
