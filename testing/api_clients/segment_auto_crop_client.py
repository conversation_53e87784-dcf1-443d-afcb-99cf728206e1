"""
Client for Segment Auto Crop API.

Handles requests to the segment auto crop endpoint with support for:
- Image upload and processing
- Automatic perspective transformation
- Segment alignment and cropping
"""

import requests
import json
import time
from typing import Dict, Any
from pathlib import Path


class SegmentAutoCropClient:
    """Client for Segment Auto Crop API (Port 8388)."""
    
    def __init__(self, base_url: str = "http://localhost:8388", timeout: int = 30):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
    
    def segment_crop(self, image_path: str) -> Dict[str, Any]:
        """
        Process an image using the segment auto crop API.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            API response dictionary
            
        Raises:
            requests.RequestException: If the request fails
            FileNotFoundError: If the image file doesn't exist
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        url = f"{self.base_url}/segment_crop"
        
        # Prepare file upload
        with open(image_path, 'rb') as f:
            files = {'file': (image_path.name, f, 'image/jpeg')}
            
            start_time = time.time()
            response = self.session.post(
                url,
                files=files,
                timeout=self.timeout
            )
            processing_time = time.time() - start_time
        
        # Handle response
        if response.status_code == 200:
            result = response.json()
            result['_metadata'] = {
                'processing_time': processing_time,
                'status_code': response.status_code,
                'api': 'segment_auto_crop'
            }
            return result
        else:
            error_msg = f"API request failed with status {response.status_code}"
            try:
                error_detail = response.json()
                error_msg += f": {error_detail}"
            except:
                error_msg += f": {response.text}"
            raise requests.RequestException(error_msg)
    
    def health_check(self) -> bool:
        """
        Check if the API is healthy and responding.
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            url = f"{self.base_url}/health"
            response = self.session.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get API information and capabilities.
        
        Returns:
            API information dictionary
        """
        return {
            'name': 'Segment Auto Crop API',
            'port': 8388,
            'endpoint': '/segment_crop',
            'description': 'Segmentation with automatic cropping and perspective transformation',
            'parameters': {
                'file': 'Image file upload (required)'
            },
            'response_fields': [
                'detection.predictions',
                'warped_image_url',
                'warped_image_filename'
            ],
            'typical_processing_time': '3-8 seconds'
        }


class SegmentAutoCropTestSuite:
    """Test suite for Segment Auto Crop API."""
    
    def __init__(self, client: SegmentAutoCropClient):
        self.client = client
        self.test_results = []
    
    def run_basic_test(self, image_path: str) -> Dict[str, Any]:
        """Run basic functionality test."""
        try:
            result = self.client.segment_crop(image_path)
            
            # Validate response structure
            required_fields = ['detection']
            missing_fields = [field for field in required_fields if field not in result]
            
            # Check if detection has predictions
            has_predictions = (
                'detection' in result and 
                'predictions' in result['detection'] and 
                isinstance(result['detection']['predictions'], list)
            )
            
            test_result = {
                'test_name': 'basic_functionality',
                'success': len(missing_fields) == 0 and has_predictions,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'missing_fields': missing_fields,
                'has_predictions': has_predictions,
                'prediction_count': len(result.get('detection', {}).get('predictions', [])),
                'has_warped_image_url': 'warped_image_url' in result,
                'response_size': len(str(result))
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'basic_functionality',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_image_quality_tests(self, image_paths: list) -> Dict[str, Any]:
        """Run tests with different image qualities."""
        results = []
        
        for i, image_path in enumerate(image_paths):
            try:
                result = self.client.segment_crop(image_path)
                test_result = {
                    'test_name': f'image_quality_test_{i+1}',
                    'image_path': str(image_path),
                    'success': True,
                    'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                    'prediction_count': len(result.get('detection', {}).get('predictions', []))
                }
            except Exception as e:
                test_result = {
                    'test_name': f'image_quality_test_{i+1}',
                    'image_path': str(image_path),
                    'success': False,
                    'error': str(e)
                }
            
            results.append(test_result)
            self.test_results.append(test_result)
        
        return results
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        avg_processing_time = 0
        processing_times = [r.get('processing_time', 0) for r in self.test_results if r.get('processing_time')]
        if processing_times:
            avg_processing_time = sum(processing_times) / len(processing_times)
        
        total_predictions = sum(r.get('prediction_count', 0) for r in self.test_results if r.get('success'))
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'average_processing_time': avg_processing_time,
            'total_predictions': total_predictions,
            'test_results': self.test_results
        }


if __name__ == "__main__":
    # Example usage
    client = SegmentAutoCropClient()
    
    # Check if API is healthy
    if client.health_check():
        print("✅ Segment Auto Crop API is healthy")
        
        # Run a test (replace with actual image path)
        # result = client.segment_crop("sample_images/core_sample_1.jpg")
        # print(f"Processing completed in {result['_metadata']['processing_time']:.2f} seconds")
        # print(f"Found {len(result['detection']['predictions'])} segments")
    else:
        print("❌ Segment Auto Crop API is not responding")
