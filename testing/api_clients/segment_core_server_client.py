"""
Client for Segment Core Server API.

Handles requests to the segment core server endpoint with support for:
- Image upload and processing
- Block processing with OCR and AI text processing
- Custom prompts and depth range parameters
- Row transformation options
"""

import requests
import json
import time
from typing import Dict, Any, Optional
from pathlib import Path


class SegmentCoreServerClient:
    """Client for Segment Core Server API (Port 8389)."""
    
    def __init__(self, base_url: str = "http://localhost:8389", timeout: int = 60):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
    
    def process_core_outline(
        self,
        image_path: str,
        use_row: bool = True,
        depth_from: float = 0.0,
        depth_to: float = 2.4,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process an image using the segment core server API.
        
        Args:
            image_path: Path to the image file
            use_row: Whether to apply perspective transformation
            depth_from: Minimum depth value for Gemini processing
            depth_to: Maximum depth value for Gemini processing
            custom_prompt: Optional custom prompt for Gemini AI
            
        Returns:
            API response dictionary
            
        Raises:
            requests.RequestException: If the request fails
            FileNotFoundError: If the image file doesn't exist
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        url = f"{self.base_url}/process_core_outline"
        
        # Prepare form data
        data = {
            'use_row': str(use_row).lower(),
            'depth_from': str(depth_from),
            'depth_to': str(depth_to)
        }
        
        if custom_prompt:
            data['custom_prompt'] = custom_prompt
        
        # Prepare file upload
        with open(image_path, 'rb') as f:
            files = {'file': (image_path.name, f, 'image/jpeg')}
            
            start_time = time.time()
            response = self.session.post(
                url,
                data=data,
                files=files,
                timeout=self.timeout
            )
            processing_time = time.time() - start_time
        
        # Handle response
        if response.status_code == 200:
            result = response.json()
            result['_metadata'] = {
                'processing_time': processing_time,
                'status_code': response.status_code,
                'api': 'segment_core_server'
            }
            return result
        else:
            error_msg = f"API request failed with status {response.status_code}"
            try:
                error_detail = response.json()
                error_msg += f": {error_detail}"
            except:
                error_msg += f": {response.text}"
            raise requests.RequestException(error_msg)
    
    def health_check(self) -> bool:
        """
        Check if the API is healthy and responding.
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            url = f"{self.base_url}/health"
            response = self.session.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get API information and capabilities.
        
        Returns:
            API information dictionary
        """
        return {
            'name': 'Segment Core Server API',
            'port': 8389,
            'endpoint': '/process_core_outline',
            'description': 'Most comprehensive API with Google OCR and Gemini integration',
            'parameters': {
                'use_row': 'bool - Apply perspective transformation',
                'depth_from': 'float - Minimum depth value',
                'depth_to': 'float - Maximum depth value',
                'custom_prompt': 'str - Optional custom prompt for Gemini AI'
            },
            'response_fields': [
                'blocks',
                'warped_image_url'
            ],
            'typical_processing_time': '8-20 seconds'
        }


class SegmentCoreServerTestSuite:
    """Test suite for Segment Core Server API."""
    
    def __init__(self, client: SegmentCoreServerClient):
        self.client = client
        self.test_results = []
    
    def run_basic_test(self, image_path: str) -> Dict[str, Any]:
        """Run basic functionality test."""
        try:
            result = self.client.process_core_outline(image_path)
            
            # Validate response structure
            required_fields = ['blocks']
            missing_fields = [field for field in required_fields if field not in result]
            
            # Check blocks structure
            blocks = result.get('blocks', [])
            has_valid_blocks = isinstance(blocks, list) and len(blocks) > 0
            
            # Check if blocks have required fields
            block_fields_valid = True
            if has_valid_blocks:
                required_block_fields = ['class', 'x', 'y', 'width', 'height', 'confidence']
                for block in blocks:
                    if not all(field in block for field in required_block_fields):
                        block_fields_valid = False
                        break
            
            # Count blocks with text processing results
            blocks_with_google_text = sum(1 for block in blocks if block.get('google_text'))
            blocks_with_gemini_text = sum(1 for block in blocks if block.get('gemini_text'))
            
            test_result = {
                'test_name': 'basic_functionality',
                'success': len(missing_fields) == 0 and has_valid_blocks and block_fields_valid,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'missing_fields': missing_fields,
                'has_valid_blocks': has_valid_blocks,
                'block_fields_valid': block_fields_valid,
                'total_blocks': len(blocks),
                'blocks_with_google_text': blocks_with_google_text,
                'blocks_with_gemini_text': blocks_with_gemini_text,
                'has_warped_image_url': 'warped_image_url' in result,
                'response_size': len(str(result))
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'basic_functionality',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_text_processing_test(self, image_path: str) -> Dict[str, Any]:
        """Run test focused on text processing capabilities."""
        try:
            # Test with custom prompt
            result = self.client.process_core_outline(
                image_path,
                custom_prompt="Identify geological features and depth measurements"
            )
            
            blocks = result.get('blocks', [])
            
            # Analyze text processing results
            google_texts = [block.get('google_text', '') for block in blocks if block.get('google_text')]
            gemini_texts = [block.get('gemini_text', '') for block in blocks if block.get('gemini_text')]
            
            # Calculate text lengths
            avg_google_text_length = sum(len(text) for text in google_texts) / len(google_texts) if google_texts else 0
            avg_gemini_text_length = sum(len(text) for text in gemini_texts) / len(gemini_texts) if gemini_texts else 0
            
            test_result = {
                'test_name': 'text_processing',
                'success': len(google_texts) > 0 or len(gemini_texts) > 0,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'total_blocks': len(blocks),
                'blocks_with_google_text': len(google_texts),
                'blocks_with_gemini_text': len(gemini_texts),
                'avg_google_text_length': avg_google_text_length,
                'avg_gemini_text_length': avg_gemini_text_length,
                'sample_google_text': google_texts[0] if google_texts else None,
                'sample_gemini_text': gemini_texts[0] if gemini_texts else None
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'text_processing',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_parameter_tests(self, image_path: str) -> Dict[str, Any]:
        """Run tests with different parameter combinations."""
        test_cases = [
            {'use_row': True, 'depth_from': 0.0, 'depth_to': 1.0},
            {'use_row': False, 'depth_from': 1.0, 'depth_to': 2.0},
            {'use_row': True, 'custom_prompt': 'Extract depth measurements'},
        ]
        
        results = []
        for i, params in enumerate(test_cases):
            try:
                result = self.client.process_core_outline(image_path, **params)
                blocks = result.get('blocks', [])
                
                test_result = {
                    'test_name': f'parameter_test_{i+1}',
                    'parameters': params,
                    'success': True,
                    'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                    'total_blocks': len(blocks),
                    'blocks_with_text': sum(1 for b in blocks if b.get('google_text') or b.get('gemini_text'))
                }
            except Exception as e:
                test_result = {
                    'test_name': f'parameter_test_{i+1}',
                    'parameters': params,
                    'success': False,
                    'error': str(e)
                }
            
            results.append(test_result)
            self.test_results.append(test_result)
        
        return results
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        avg_processing_time = 0
        processing_times = [r.get('processing_time', 0) for r in self.test_results if r.get('processing_time')]
        if processing_times:
            avg_processing_time = sum(processing_times) / len(processing_times)
        
        total_blocks = sum(r.get('total_blocks', 0) for r in self.test_results if r.get('success'))
        total_blocks_with_text = sum(r.get('blocks_with_google_text', 0) + r.get('blocks_with_gemini_text', 0) for r in self.test_results if r.get('success'))
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'average_processing_time': avg_processing_time,
            'total_blocks': total_blocks,
            'total_blocks_with_text': total_blocks_with_text,
            'test_results': self.test_results
        }


if __name__ == "__main__":
    # Example usage
    client = SegmentCoreServerClient()
    
    # Check if API is healthy
    if client.health_check():
        print("✅ Segment Core Server API is healthy")
        
        # Run a test (replace with actual image path)
        # result = client.process_core_outline("sample_images/core_sample_1.jpg")
        # print(f"Processing completed in {result['_metadata']['processing_time']:.2f} seconds")
        # print(f"Found {len(result['blocks'])} blocks")
        # blocks_with_text = sum(1 for b in result['blocks'] if b.get('google_text') or b.get('gemini_text'))
        # print(f"{blocks_with_text} blocks have text processing results")
    else:
        print("❌ Segment Core Server API is not responding")
