"""
API clients for testing aibase-ml APIs.

This package provides client classes for interacting with all 4 APIs:
- MainProcessingClient: Complete processing pipeline
- SegmentAutoCropClient: Segmentation with auto cropping
- SegmentCorePiecesClient: Core piece extraction
- SegmentCoreServerClient: Block processing with OCR/AI

Each client handles:
- HTTP requests and file uploads
- Response parsing and validation
- Error handling and retries
- Timeout management
"""

from .main_processing_client import MainProcessingClient
from .segment_auto_crop_client import SegmentAutoCropClient
from .segment_core_pieces_client import SegmentCorePiecesClient
from .segment_core_server_client import SegmentCoreServerClient

__all__ = [
    'MainProcessingClient',
    'SegmentAutoCropClient', 
    'SegmentCorePiecesClient',
    'SegmentCoreServerClient'
]
