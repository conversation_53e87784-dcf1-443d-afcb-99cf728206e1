"""
Client for Segment Core Pieces API.

Handles requests to the segment core pieces endpoint with support for:
- Image upload and processing
- Core piece extraction and transformation
- Segment alignment to box boundaries
"""

import requests
import json
import time
from typing import Dict, Any
from pathlib import Path


class SegmentCorePiecesClient:
    """Client for Segment Core Pieces API (Port 8381)."""
    
    def __init__(self, base_url: str = "http://localhost:8381", timeout: int = 30):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
    
    def segment_core_pieces(self, image_path: str) -> Dict[str, Any]:
        """
        Process an image using the segment core pieces API.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            API response dictionary
            
        Raises:
            requests.RequestException: If the request fails
            FileNotFoundError: If the image file doesn't exist
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        url = f"{self.base_url}/segment_core_pieces"
        
        # Prepare file upload
        with open(image_path, 'rb') as f:
            files = {'file': (image_path.name, f, 'image/jpeg')}
            
            start_time = time.time()
            response = self.session.post(
                url,
                files=files,
                timeout=self.timeout
            )
            processing_time = time.time() - start_time
        
        # Handle response
        if response.status_code == 200:
            result = response.json()
            result['_metadata'] = {
                'processing_time': processing_time,
                'status_code': response.status_code,
                'api': 'segment_core_pieces'
            }
            return result
        else:
            error_msg = f"API request failed with status {response.status_code}"
            try:
                error_detail = response.json()
                error_msg += f": {error_detail}"
            except:
                error_msg += f": {response.text}"
            raise requests.RequestException(error_msg)
    
    def health_check(self) -> bool:
        """
        Check if the API is healthy and responding.
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            url = f"{self.base_url}/health"
            response = self.session.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get API information and capabilities.
        
        Returns:
            API information dictionary
        """
        return {
            'name': 'Segment Core Pieces API',
            'port': 8381,
            'endpoint': '/segment_core_pieces',
            'description': 'Core segmentation with perspective transformation and piece extraction',
            'parameters': {
                'file': 'Image file upload (required)'
            },
            'response_fields': [
                'segmentation.predictions',
                'warped_image_url'
            ],
            'typical_processing_time': '3-8 seconds'
        }


class SegmentCorePiecesTestSuite:
    """Test suite for Segment Core Pieces API."""
    
    def __init__(self, client: SegmentCorePiecesClient):
        self.client = client
        self.test_results = []
    
    def run_basic_test(self, image_path: str) -> Dict[str, Any]:
        """Run basic functionality test."""
        try:
            result = self.client.segment_core_pieces(image_path)
            
            # Validate response structure
            required_fields = ['segmentation']
            missing_fields = [field for field in required_fields if field not in result]
            
            # Check if segmentation has predictions
            has_predictions = (
                'segmentation' in result and 
                'predictions' in result['segmentation'] and 
                isinstance(result['segmentation']['predictions'], list)
            )
            
            # Count core pieces (should be converted to Row class)
            core_pieces = []
            if has_predictions:
                core_pieces = [
                    pred for pred in result['segmentation']['predictions'] 
                    if pred.get('class') == 'Row'
                ]
            
            test_result = {
                'test_name': 'basic_functionality',
                'success': len(missing_fields) == 0 and has_predictions,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'missing_fields': missing_fields,
                'has_predictions': has_predictions,
                'total_predictions': len(result.get('segmentation', {}).get('predictions', [])),
                'core_pieces_count': len(core_pieces),
                'has_warped_image_url': 'warped_image_url' in result,
                'response_size': len(str(result))
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'basic_functionality',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_core_analysis_test(self, image_path: str) -> Dict[str, Any]:
        """Run test focused on core piece analysis."""
        try:
            result = self.client.segment_core_pieces(image_path)
            
            # Analyze core pieces
            predictions = result.get('segmentation', {}).get('predictions', [])
            
            # Group by class
            class_counts = {}
            for pred in predictions:
                class_name = pred.get('class', 'Unknown')
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            # Analyze core pieces specifically
            core_pieces = [pred for pred in predictions if pred.get('class') == 'Row']
            
            # Calculate average confidence
            avg_confidence = 0
            if core_pieces:
                avg_confidence = sum(pred.get('confidence', 0) for pred in core_pieces) / len(core_pieces)
            
            test_result = {
                'test_name': 'core_analysis',
                'success': len(core_pieces) > 0,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'class_distribution': class_counts,
                'core_pieces_count': len(core_pieces),
                'average_confidence': avg_confidence,
                'min_confidence': min((pred.get('confidence', 0) for pred in core_pieces), default=0),
                'max_confidence': max((pred.get('confidence', 0) for pred in core_pieces), default=0)
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'core_analysis',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        avg_processing_time = 0
        processing_times = [r.get('processing_time', 0) for r in self.test_results if r.get('processing_time')]
        if processing_times:
            avg_processing_time = sum(processing_times) / len(processing_times)
        
        total_core_pieces = sum(r.get('core_pieces_count', 0) for r in self.test_results if r.get('success'))
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'average_processing_time': avg_processing_time,
            'total_core_pieces': total_core_pieces,
            'test_results': self.test_results
        }


if __name__ == "__main__":
    # Example usage
    client = SegmentCorePiecesClient()
    
    # Check if API is healthy
    if client.health_check():
        print("✅ Segment Core Pieces API is healthy")
        
        # Run a test (replace with actual image path)
        # result = client.segment_core_pieces("sample_images/core_sample_1.jpg")
        # print(f"Processing completed in {result['_metadata']['processing_time']:.2f} seconds")
        # core_pieces = [p for p in result['segmentation']['predictions'] if p.get('class') == 'Row']
        # print(f"Found {len(core_pieces)} core pieces")
    else:
        print("❌ Segment Core Pieces API is not responding")
