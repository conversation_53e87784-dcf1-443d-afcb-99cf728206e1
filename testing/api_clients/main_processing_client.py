"""
Client for Main Processing API.

Handles requests to the main processing endpoint with support for:
- Image upload and processing
- Segmentation options
- Custom prompts
- Depth range parameters
"""

import requests
import json
import time
from typing import Dict, Any, Optional
from pathlib import Path


class MainProcessingClient:
    """Client for Main Processing API (Port 8386)."""
    
    def __init__(self, base_url: str = "http://localhost:8386", timeout: int = 60):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL for the API
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
    
    def process_image(
        self,
        image_path: str,
        use_segmentation: bool = True,
        depth_from: float = 0.0,
        depth_to: float = 2.4,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process an image using the main processing API.
        
        Args:
            image_path: Path to the image file
            use_segmentation: Whether to apply perspective transformation
            depth_from: Minimum depth value for Gemini processing
            depth_to: Maximum depth value for Gemini processing
            custom_prompt: Optional custom prompt for Gemini AI
            
        Returns:
            API response dictionary
            
        Raises:
            requests.RequestException: If the request fails
            FileNotFoundError: If the image file doesn't exist
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        url = f"{self.base_url}/process"
        
        # Prepare form data
        data = {
            'use_segmentation': str(use_segmentation).lower(),
            'depth_from': str(depth_from),
            'depth_to': str(depth_to)
        }
        
        if custom_prompt:
            data['custom_prompt'] = custom_prompt
        
        # Prepare file upload
        with open(image_path, 'rb') as f:
            files = {'file': (image_path.name, f, 'image/jpeg')}
            
            start_time = time.time()
            response = self.session.post(
                url,
                data=data,
                files=files,
                timeout=self.timeout
            )
            processing_time = time.time() - start_time
        
        # Handle response
        if response.status_code == 200:
            result = response.json()
            result['_metadata'] = {
                'processing_time': processing_time,
                'status_code': response.status_code,
                'api': 'main_processing'
            }
            return result
        else:
            error_msg = f"API request failed with status {response.status_code}"
            try:
                error_detail = response.json()
                error_msg += f": {error_detail}"
            except:
                error_msg += f": {response.text}"
            raise requests.RequestException(error_msg)
    
    def health_check(self) -> bool:
        """
        Check if the API is healthy and responding.
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            url = f"{self.base_url}/health"
            response = self.session.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        Get API information and capabilities.
        
        Returns:
            API information dictionary
        """
        return {
            'name': 'Main Processing API',
            'port': 8386,
            'endpoint': '/process',
            'description': 'Complete processing pipeline with OCR and AI text processing',
            'parameters': {
                'use_segmentation': 'bool - Apply perspective transformation',
                'depth_from': 'float - Minimum depth value',
                'depth_to': 'float - Maximum depth value', 
                'custom_prompt': 'str - Optional custom prompt for Gemini AI'
            },
            'response_fields': [
                'warped_image',
                'google_result',
                'gemini_result',
                'x', 'y', 'width', 'height'
            ],
            'typical_processing_time': '5-15 seconds'
        }


class MainProcessingTestSuite:
    """Test suite for Main Processing API."""
    
    def __init__(self, client: MainProcessingClient):
        self.client = client
        self.test_results = []
    
    def run_basic_test(self, image_path: str) -> Dict[str, Any]:
        """Run basic functionality test."""
        try:
            result = self.client.process_image(image_path)
            
            # Validate response structure
            required_fields = ['warped_image', 'google_result', 'gemini_result', 'x', 'y', 'width', 'height']
            missing_fields = [field for field in required_fields if field not in result]
            
            test_result = {
                'test_name': 'basic_functionality',
                'success': len(missing_fields) == 0,
                'processing_time': result.get('_metadata', {}).get('processing_time', 0),
                'missing_fields': missing_fields,
                'response_size': len(str(result))
            }
            
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            test_result = {
                'test_name': 'basic_functionality',
                'success': False,
                'error': str(e)
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_parameter_tests(self, image_path: str) -> Dict[str, Any]:
        """Run tests with different parameter combinations."""
        test_cases = [
            {'use_segmentation': True, 'depth_from': 0.0, 'depth_to': 1.0},
            {'use_segmentation': False, 'depth_from': 1.0, 'depth_to': 2.0},
            {'use_segmentation': True, 'custom_prompt': 'Extract geological features'},
        ]
        
        results = []
        for i, params in enumerate(test_cases):
            try:
                result = self.client.process_image(image_path, **params)
                test_result = {
                    'test_name': f'parameter_test_{i+1}',
                    'parameters': params,
                    'success': True,
                    'processing_time': result.get('_metadata', {}).get('processing_time', 0)
                }
            except Exception as e:
                test_result = {
                    'test_name': f'parameter_test_{i+1}',
                    'parameters': params,
                    'success': False,
                    'error': str(e)
                }
            
            results.append(test_result)
            self.test_results.append(test_result)
        
        return results
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get summary of all test results."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        avg_processing_time = 0
        processing_times = [r.get('processing_time', 0) for r in self.test_results if r.get('processing_time')]
        if processing_times:
            avg_processing_time = sum(processing_times) / len(processing_times)
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'average_processing_time': avg_processing_time,
            'test_results': self.test_results
        }


if __name__ == "__main__":
    # Example usage
    client = MainProcessingClient()

    # Check if API is healthy
    if client.health_check():
        print("✅ Main Processing API is healthy")

        # Run a test (replace with actual image path)
        # result = client.process_image("sample_images/core_sample_1.jpg")
        # print(f"Processing completed in {result['_metadata']['processing_time']:.2f} seconds")
    else:
        print("❌ Main Processing API is not responding")
