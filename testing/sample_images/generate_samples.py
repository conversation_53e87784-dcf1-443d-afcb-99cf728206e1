#!/usr/bin/env python3
"""
Generate sample images for API testing.

Creates synthetic test images that simulate core drilling samples
with various characteristics for comprehensive API testing.
"""

import cv2
import numpy as np
from pathlib import Path
import argparse


def create_core_sample_image(width: int = 800, height: int = 1200, filename: str = "core_sample.jpg") -> str:
    """
    Create a synthetic core drilling sample image.
    
    Args:
        width: Image width
        height: Image height
        filename: Output filename
        
    Returns:
        Path to created image
    """
    # Create base image with gradient background
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add gradient background (simulating rock layers)
    for y in range(height):
        intensity = int(120 + 60 * np.sin(y * 0.01))
        image[y, :] = [intensity, intensity - 20, intensity - 40]
    
    # Add box outline (drilling box)
    box_margin = 50
    box_thickness = 8
    cv2.rectangle(image, (box_margin, box_margin), 
                 (width - box_margin, height - box_margin), 
                 (0, 0, 255), box_thickness)
    
    # Add core segments (horizontal bands)
    segment_height = 80
    num_segments = (height - 2 * box_margin) // segment_height
    
    for i in range(num_segments):
        y_start = box_margin + i * segment_height + 10
        y_end = y_start + segment_height - 20
        
        # Vary segment colors (different rock types)
        colors = [
            (139, 69, 19),   # Brown
            (160, 82, 45),   # Saddle brown
            (210, 180, 140), # Tan
            (105, 105, 105), # Dim gray
            (128, 128, 0),   # Olive
        ]
        color = colors[i % len(colors)]
        
        # Draw segment rectangle
        cv2.rectangle(image, (box_margin + 15, y_start), 
                     (width - box_margin - 15, y_end), color, -1)
        
        # Add some texture/noise
        noise = np.random.randint(-30, 30, (y_end - y_start, width - 2 * box_margin - 30, 3))
        segment_region = image[y_start:y_end, box_margin + 15:width - box_margin - 15]
        segment_region = np.clip(segment_region.astype(int) + noise, 0, 255).astype(np.uint8)
        image[y_start:y_end, box_margin + 15:width - box_margin - 15] = segment_region
        
        # Add depth markers (text)
        depth = i * 0.3  # 30cm per segment
        cv2.putText(image, f"{depth:.1f}m", 
                   (width - box_margin + 10, y_start + 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # Add some geological features
    # Fractures
    for _ in range(3):
        x1 = np.random.randint(box_margin + 20, width - box_margin - 20)
        y1 = np.random.randint(box_margin + 20, height - box_margin - 20)
        x2 = x1 + np.random.randint(-50, 50)
        y2 = y1 + np.random.randint(50, 150)
        cv2.line(image, (x1, y1), (x2, y2), (0, 0, 0), 2)
    
    # Mineral deposits (circles)
    for _ in range(5):
        x = np.random.randint(box_margin + 30, width - box_margin - 30)
        y = np.random.randint(box_margin + 30, height - box_margin - 30)
        radius = np.random.randint(5, 15)
        color = (np.random.randint(200, 255), np.random.randint(200, 255), 0)
        cv2.circle(image, (x, y), radius, color, -1)
    
    # Save image
    output_path = Path(__file__).parent / filename
    cv2.imwrite(str(output_path), image)
    print(f"Created sample image: {output_path}")
    
    return str(output_path)


def create_box_sample_image(width: int = 600, height: int = 800, filename: str = "box_sample.jpg") -> str:
    """
    Create a sample image with clear box boundaries for testing.
    
    Args:
        width: Image width
        height: Image height
        filename: Output filename
        
    Returns:
        Path to created image
    """
    # Create base image
    image = np.full((height, width, 3), (240, 240, 240), dtype=np.uint8)
    
    # Add main box
    box_x, box_y = 100, 100
    box_w, box_h = width - 200, height - 200
    
    # Box outline
    cv2.rectangle(image, (box_x, box_y), (box_x + box_w, box_y + box_h), (0, 0, 255), 5)
    
    # Fill box with pattern
    cv2.rectangle(image, (box_x + 10, box_y + 10), 
                 (box_x + box_w - 10, box_y + box_h - 10), (200, 200, 200), -1)
    
    # Add core pieces inside box
    piece_height = 60
    num_pieces = box_h // piece_height
    
    for i in range(num_pieces - 1):
        y_pos = box_y + 20 + i * piece_height
        
        # Alternate colors
        color = (150, 100, 50) if i % 2 == 0 else (100, 150, 100)
        
        cv2.rectangle(image, (box_x + 20, y_pos), 
                     (box_x + box_w - 20, y_pos + piece_height - 10), color, -1)
        
        # Add piece boundary
        cv2.rectangle(image, (box_x + 20, y_pos), 
                     (box_x + box_w - 20, y_pos + piece_height - 10), (0, 0, 0), 2)
    
    # Add some text blocks
    for i in range(3):
        x = box_x + 30 + i * 100
        y = box_y + box_h + 30
        cv2.rectangle(image, (x, y), (x + 80, y + 40), (255, 255, 255), -1)
        cv2.rectangle(image, (x, y), (x + 80, y + 40), (0, 0, 0), 2)
        cv2.putText(image, f"Block {i+1}", (x + 5, y + 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    # Save image
    output_path = Path(__file__).parent / filename
    cv2.imwrite(str(output_path), image)
    print(f"Created box sample image: {output_path}")
    
    return str(output_path)


def create_mixed_sample_image(width: int = 1000, height: int = 800, filename: str = "mixed_sample.jpg") -> str:
    """
    Create a complex sample image with multiple elements.
    
    Args:
        width: Image width
        height: Image height
        filename: Output filename
        
    Returns:
        Path to created image
    """
    # Create base image with texture
    image = np.random.randint(180, 220, (height, width, 3), dtype=np.uint8)
    
    # Add multiple boxes
    boxes = [
        (50, 50, 300, 400),
        (400, 100, 250, 300),
        (700, 200, 200, 350)
    ]
    
    for i, (x, y, w, h) in enumerate(boxes):
        # Box outline
        cv2.rectangle(image, (x, y), (x + w, y + h), (255, 0, 0), 3)
        
        # Fill with different patterns
        if i == 0:
            # Horizontal stripes
            for stripe in range(0, h, 30):
                color = (100, 150, 200) if stripe // 30 % 2 == 0 else (150, 100, 50)
                cv2.rectangle(image, (x + 5, y + stripe), (x + w - 5, y + stripe + 25), color, -1)
        
        elif i == 1:
            # Vertical sections
            section_width = w // 3
            colors = [(200, 100, 100), (100, 200, 100), (100, 100, 200)]
            for j in range(3):
                cv2.rectangle(image, (x + j * section_width, y + 5), 
                             (x + (j + 1) * section_width, y + h - 5), colors[j], -1)
        
        else:
            # Random blocks
            for _ in range(8):
                bx = x + np.random.randint(10, w - 40)
                by = y + np.random.randint(10, h - 40)
                bw, bh = 30, 30
                color = tuple(np.random.randint(50, 200, 3).tolist())
                cv2.rectangle(image, (bx, by), (bx + bw, by + bh), color, -1)
    
    # Add text regions
    text_regions = [
        (100, 500, "Sample A - Depth 0-2m"),
        (450, 450, "Sample B - Depth 2-4m"),
        (750, 600, "Sample C - Depth 4-6m")
    ]
    
    for x, y, text in text_regions:
        # Text background
        text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        cv2.rectangle(image, (x - 5, y - text_size[1] - 5), 
                     (x + text_size[0] + 5, y + 5), (255, 255, 255), -1)
        cv2.rectangle(image, (x - 5, y - text_size[1] - 5), 
                     (x + text_size[0] + 5, y + 5), (0, 0, 0), 2)
        
        # Text
        cv2.putText(image, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # Save image
    output_path = Path(__file__).parent / filename
    cv2.imwrite(str(output_path), image)
    print(f"Created mixed sample image: {output_path}")
    
    return str(output_path)


def create_all_samples():
    """Create all sample images."""
    print("🎨 Generating sample images for API testing...")
    
    # Create output directory
    output_dir = Path(__file__).parent
    output_dir.mkdir(exist_ok=True)
    
    # Generate different sample types
    samples = [
        (create_core_sample_image, "core_sample_1.jpg"),
        (create_core_sample_image, "core_sample_2.jpg"),
        (create_box_sample_image, "box_sample.jpg"),
        (create_mixed_sample_image, "mixed_sample.jpg")
    ]
    
    created_files = []
    for create_func, filename in samples:
        try:
            if create_func == create_core_sample_image and filename == "core_sample_2.jpg":
                # Create a different variant
                path = create_func(width=600, height=1000, filename=filename)
            else:
                path = create_func(filename=filename)
            created_files.append(path)
        except Exception as e:
            print(f"❌ Failed to create {filename}: {e}")
    
    print(f"✅ Created {len(created_files)} sample images:")
    for path in created_files:
        print(f"   📷 {path}")
    
    return created_files


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Generate sample images for API testing')
    parser.add_argument('--type', choices=['core', 'box', 'mixed', 'all'], default='all',
                       help='Type of sample to generate')
    parser.add_argument('--filename', help='Output filename (for single image generation)')
    parser.add_argument('--width', type=int, default=800, help='Image width')
    parser.add_argument('--height', type=int, default=1200, help='Image height')
    
    args = parser.parse_args()
    
    if args.type == 'all':
        create_all_samples()
    else:
        filename = args.filename or f"{args.type}_sample.jpg"
        
        if args.type == 'core':
            create_core_sample_image(args.width, args.height, filename)
        elif args.type == 'box':
            create_box_sample_image(args.width, args.height, filename)
        elif args.type == 'mixed':
            create_mixed_sample_image(args.width, args.height, filename)


if __name__ == "__main__":
    main()
