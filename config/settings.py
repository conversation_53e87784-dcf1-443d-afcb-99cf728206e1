"""Centralized configuration management for aibase-ml application."""

import os
from typing import Optional
from dataclasses import dataclass


@dataclass
class Settings:
    """Application settings with environment variable support."""
    
    # API Keys
    roboflow_api_key: str = "hM2hVNK6d4UFxGMXjXkT"
    google_vision_api_key: str = "AIzaSyDCnITbjPc9xQckAz3_xD37v0-v2ScAUTc"
    gemini_api_key: str = "AIzaSyABOdc6lzJpu77O_M5IAWUUBdfeFQ-dja0"
    
    # Azure Storage Configuration
    azure_storage_connection_string: str = (
        "DefaultEndpointsProtocol=https;AccountName=aibaseimagestorage;"
        "AccountKey=****************************************************************************************;"
        "EndpointSuffix=core.windows.net"
    )
    azure_container_name: str = "images-stag"
    azure_warped_container_path: str = "images-stag/warped_image"
    
    # Roboflow Model Configuration
    box_core_block_model_id: str = "box-core-block-segmentation/7"
    box_core_block_model_id_v6: str = "box-core-block-segmentation/6"
    
    # Processing Configuration
    confidence_threshold: float = 0.5
    default_extent: int = 10
    default_padding: int = 150
    default_iou_threshold: float = 0.5
    y_tolerance: int = 50
    
    # Server Configuration
    host: str = "0.0.0.0"
    debug: bool = True
    
    # API Ports
    main_api_port: int = 8386
    auto_crop_api_port: int = 8388
    core_pieces_api_port: int = 8381
    core_outline_api_port: int = 8389
    
    # Logging Configuration
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Default Processing Parameters
    default_depth_from: float = 0.0
    default_depth_to: float = 2.4
    
    def __post_init__(self):
        """Load environment variables after initialization."""
        self._load_from_env()
    
    def _load_from_env(self):
        """Load configuration from environment variables."""
        # API Keys
        self.roboflow_api_key = os.getenv("ROBOFLOW_API_KEY", self.roboflow_api_key)
        self.google_vision_api_key = os.getenv("GOOGLE_VISION_API_KEY", self.google_vision_api_key)
        self.gemini_api_key = os.getenv("GEMINI_API_KEY", self.gemini_api_key)
        
        # Azure Storage
        self.azure_storage_connection_string = os.getenv(
            "AZURE_STORAGE_CONNECTION_STRING", 
            self.azure_storage_connection_string
        )
        self.azure_container_name = os.getenv("AZURE_CONTAINER_NAME", self.azure_container_name)
        
        # Server Configuration
        self.host = os.getenv("HOST", self.host)
        self.debug = os.getenv("DEBUG", str(self.debug)).lower() == "true"
        
        # Ports
        self.main_api_port = int(os.getenv("MAIN_API_PORT", str(self.main_api_port)))
        self.auto_crop_api_port = int(os.getenv("AUTO_CROP_API_PORT", str(self.auto_crop_api_port)))
        self.core_pieces_api_port = int(os.getenv("CORE_PIECES_API_PORT", str(self.core_pieces_api_port)))
        self.core_outline_api_port = int(os.getenv("CORE_OUTLINE_API_PORT", str(self.core_outline_api_port)))
        
        # Processing Parameters
        self.confidence_threshold = float(os.getenv("CONFIDENCE_THRESHOLD", str(self.confidence_threshold)))
        self.default_depth_from = float(os.getenv("DEFAULT_DEPTH_FROM", str(self.default_depth_from)))
        self.default_depth_to = float(os.getenv("DEFAULT_DEPTH_TO", str(self.default_depth_to)))
        
        # Logging
        self.log_level = os.getenv("LOG_LEVEL", self.log_level)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings() -> Settings:
    """Reload settings from environment variables."""
    global _settings
    _settings = Settings()
    return _settings
