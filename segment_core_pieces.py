from flask import Flask, request, jsonify
import cv2
import numpy as np
from copy import deepcopy
import time
from inference import get_roboflow_model
import json
import uuid
from datetime import datetime
from azure.storage.blob import BlobServiceClient

app = Flask(__name__)

class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

app.json_encoder = NumpyEncoder

ROBOFLOW_API_KEY = 'hM2hVNK6d4UFxGMXjXkT'

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=aibaseimagestorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AZURE_CONTAINER_NAME = "images-stag/warped_image"

def upload_image_to_azure(image_array, filename=None):
    """
    Upload an image array to Azure Blob Storage

    Args:
        image_array: numpy array representing the image
        filename: optional filename, if not provided, generates a unique one

    Returns:
        dict: Contains 'success', 'url', and 'filename' keys
    """
    try:
        # Generate filename if not provided
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"warped_image_{timestamp}_{unique_id}.jpg"

        # Convert image array to bytes
        success, buffer = cv2.imencode('.jpg', image_array)
        if not success:
            return {"success": False, "error": "Failed to encode image"}

        image_bytes = buffer.tobytes()

        # Create blob service client
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)

        # Get blob client
        blob_client = blob_service_client.get_blob_client(
            container=AZURE_CONTAINER_NAME,
            blob=filename
        )

        # Upload the image
        blob_client.upload_blob(image_bytes, overwrite=True, content_type='image/jpeg')

        # Generate the URL
        blob_url = f"https://aibaseimagestorage.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{filename}"

        return {
            "success": True,
            "url": blob_url,
            "filename": filename
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def run_box_core_block_segment(image):
    start = time.time()
    model = get_roboflow_model(model_id="box-core-block-segmentation/7", api_key=ROBOFLOW_API_KEY)
    result = model.infer(image)
    elapsed = time.time() - start
    return "Detection", result[0].dict(), elapsed

def transform_format(segmentation_data):
    processed_segmentations = []
    for pred in segmentation_data.get("predictions", []):
        if pred['confidence'] < 0.5:
            continue
        global_points = [
            (int(p["x"]), int(p["y"]))
            for p in pred["points"]
        ]
        segment = {
            "class": pred["class_name"],
            "x": float(pred["x"]),
            "y": float(pred["y"]),
            "width": float(pred["width"]),
            "height": float(pred["height"]),
            "confidence": float(pred["confidence"]),
            "points": [(int(x), int(y)) for x, y in global_points],
        }
        processed_segmentations.append(segment)
    return processed_segmentations

def scale_contour(contour: np.ndarray, scale: float) -> np.ndarray:
    M = cv2.moments(contour)
    if M['m00'] == 0:
        return contour
    cx = int(M['m10'] / M['m00'])
    cy = int(M['m01'] / M['m00'])
    center = np.array([[cx, cy]])
    scaled = (contour - center) * scale + center
    return scaled.astype(np.int32)

def merge_boxes_as_one_segment(segments, merged_class='Box'):
    box_segments = [seg for seg in segments if seg['class'].lower() == 'box']
    other_segments = [seg for seg in segments if seg['class'].lower() != 'box']

    if not box_segments:
        return segments

    all_points = []
    confidences = []
    for seg in box_segments:
        all_points.extend(seg['points'])
        confidences.append(seg['confidence'])

    all_points_np = np.array(all_points, dtype=np.int32)
    merged_hull = cv2.convexHull(all_points_np)
    merged_points = [(int(pt[0][0]), int(pt[0][1])) for pt in merged_hull]

    x_vals = [pt[0] for pt in merged_points]
    y_vals = [pt[1] for pt in merged_points]
    x_min, x_max = min(x_vals), max(x_vals)
    y_min, y_max = min(y_vals), max(y_vals)

    merged_segment = {
        "class": merged_class,
        "confidence": float(np.mean(confidences)),
        "x": float((x_min + x_max) / 2),
        "y": float((y_min + y_max) / 2),
        "width": float(x_max - x_min),
        "height": float(y_max - y_min),
        "points": merged_points
    }

    return other_segments + [merged_segment]

def filter_segments_inside_box(segments, merged_box_segment):
    box_contour = np.array(merged_box_segment['points'], dtype=np.int32)
    filtered_segments = []

    for seg in segments:
        if seg is merged_box_segment:
            filtered_segments.append(seg)
            continue
        center = (int(seg['x']), int(seg['y']))
        if cv2.pointPolygonTest(box_contour, center, False) >= 0:
            filtered_segments.append(seg)

    return filtered_segments

def apply_box_transform_to_segments(filter_seg, original_image, extent=10):
    box = next(item for item in filter_seg if item['class'].lower() == 'box')
    others = [item for item in filter_seg if item['class'].lower() != 'box']

    hull_points = cv2.convexHull(np.array(box['points'], dtype=np.int32))
    hull_points = scale_contour(hull_points, 1 + extent / 100)

    rect = cv2.minAreaRect(hull_points)
    box_points = cv2.boxPoints(rect).astype(np.int32)
    bbox = cv2.boundingRect(box_points)
    bbox = [max(0, x) for x in bbox]
    bbox_points = np.array([
        [bbox[0], bbox[1]],
        [bbox[0] + bbox[2], bbox[1]],
        [bbox[0] + bbox[2], bbox[1] + bbox[3]],
        [bbox[0], bbox[1] + bbox[3]]
    ], dtype=np.float32)

    epsilon = 0.02 * cv2.arcLength(hull_points, True)
    approx = cv2.approxPolyDP(hull_points, epsilon, True)

    src = []
    for bb_point in bbox_points:
        min_dis = float("inf")
        min_point = bb_point
        for point in approx:
            dis = np.linalg.norm(point.ravel() - bb_point)
            if dis < min_dis:
                min_dis = dis
                min_point = point.ravel()
        src.append(min_point)
    src = np.array(src, dtype=np.float32)

    dst = np.array([[0, 0], [bbox[2], 0], [bbox[2], bbox[3]], [0, bbox[3]]], dtype=np.float32)

    M = cv2.getPerspectiveTransform(src, dst)
    warped = cv2.warpPerspective(original_image, M, (bbox[2], bbox[3]))

    transformed_segments = []

    for seg in [box] + others:
        points = np.array(seg['points'], dtype=np.float32).reshape(-1, 1, 2)
        warped_pts = cv2.perspectiveTransform(points, M).reshape(-1, 2)
        xs, ys = warped_pts[:, 0], warped_pts[:, 1]

        transformed_segments.append({
            'class': seg['class'],
            'confidence': float(seg['confidence']),
            'x': float(np.mean(xs)),
            'y': float(np.mean(ys)),
            'width': float(np.max(xs) - np.min(xs)),
            'height': float(np.max(ys) - np.min(ys)),
            'points': [(int(x), int(y)) for x, y in warped_pts]
        })

    return warped, transformed_segments

def update_bounding_boxes_from_points(segments):
    updated_segments = []

    for seg in segments:
        pts = np.array(seg['points'], dtype=np.int32)

        x_min = np.min(pts[:, 0])
        y_min = np.min(pts[:, 1])
        x_max = np.max(pts[:, 0])
        y_max = np.max(pts[:, 1])

        new_seg = seg.copy()
        new_seg['x'] = float((x_min + x_max) / 2)
        new_seg['y'] = float((y_min + y_max) / 2)
        new_seg['width'] = float(x_max - x_min)
        new_seg['height'] = float(y_max - y_min)

        updated_segments.append(new_seg)

    return updated_segments

def align_core_bounding_boxes_to_box_segment(segments, padding=5, iou_threshold=0.8):
    def compute_iou(box1, box2):
        x1_min = box1['x'] - box1['width'] / 2
        x1_max = box1['x'] + box1['width'] / 2
        y1_min = box1['y'] - box1['height'] / 2
        y1_max = box1['y'] + box1['height'] / 2

        x2_min = box2['x'] - box2['width'] / 2
        x2_max = box2['x'] + box2['width'] / 2
        y2_min = box2['y'] - box2['height'] / 2
        y2_max = box2['y'] + box2['height'] / 2

        inter_x1 = max(x1_min, x2_min)
        inter_y1 = max(y1_min, y2_min)
        inter_x2 = min(x1_max, x2_max)
        inter_y2 = min(y1_max, y2_max)

        inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
        box1_area = (x1_max - x1_min) * (y1_max - y1_min)
        box2_area = (x2_max - x2_min) * (y2_max - y2_min)

        union_area = box1_area + box2_area - inter_area
        return inter_area / union_area if union_area > 0 else 0

    def remove_iou_duplicates(boxes, threshold):
        unique_boxes = []
        for box in boxes:
            if not any(compute_iou(box, kept) > threshold for kept in unique_boxes):
                unique_boxes.append(box)
        return unique_boxes

    box_segment = next(seg for seg in segments if seg['class'].lower() == 'box')
    x_left = int(box_segment['x'] - box_segment['width'] / 2) + padding
    x_right = int(box_segment['x'] + box_segment['width'] / 2) - padding

    core_segments = [s for s in segments if s['class'].lower() == 'core']
    if not core_segments:
        return segments

    uniform_width = x_right - x_left
    aligned_cores = []

    for seg in core_segments:
        y_center = seg['y']
        height = seg['height']

        aligned_seg = deepcopy(seg)
        aligned_seg['x'] = float((x_left + x_right) / 2)
        aligned_seg['width'] = float(uniform_width)
        aligned_seg['points'] = [
            (x_left, int(y_center - height / 2)),
            (x_right, int(y_center - height / 2)),
            (x_right, int(y_center + height / 2)),
            (x_left, int(y_center + height / 2)),
        ]
        aligned_cores.append(aligned_seg)

    aligned_cores = remove_iou_duplicates(aligned_cores, threshold=iou_threshold)
    others = [s for s in segments if s['class'].lower() != 'core']
    return others + aligned_cores

@app.route('/segment_core_pieces', methods=['POST'])
def segment_image():
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400
        
    file = request.files['image']
    # Read image file
    file_bytes = np.frombuffer(file.read(), np.uint8)
    image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
    
    if image is None:
        return jsonify({'error': 'Invalid image file'}), 400

    try:
        # Process image
        _, result, processing_time = run_box_core_block_segment(image)
        segments = transform_format(result)
        merged_segments = merge_boxes_as_one_segment(segments)
        
        merged_box = next(seg for seg in merged_segments if seg['class'].lower() == 'box')
        final_segments = filter_segments_inside_box(merged_segments, merged_box)
        
        warped_image, transformed_seg = apply_box_transform_to_segments(
            filter_seg=final_segments,
            original_image=image,
            extent=10
        )

        # Upload warped image to Azure storage
        # Create warped filename by removing extension and adding _warped.jpg
        original_filename = file.filename
        if original_filename:
            # Remove the original extension and add _warped.jpg
            name_without_ext = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
            warped_filename = f"{name_without_ext}_warped.jpg"
        else:
            warped_filename = None

        upload_result = upload_image_to_azure(warped_image, filename=warped_filename)

        transformed_seg = update_bounding_boxes_from_points(transformed_seg)
        aligned_segments = align_core_bounding_boxes_to_box_segment(
            transformed_seg,
            padding=150,
            iou_threshold=0.5
        )

        response = {
            'segmentation': {'predictions': transformed_seg}
        }

        # Add Azure upload information to response
        if upload_result["success"]:
            response['warped_image_url'] = upload_result["url"]
            response['warped_image_filename'] = upload_result["filename"]
        else:
            response['upload_error'] = upload_result["error"]

        for item in response["segmentation"]["predictions"]:
            if item.get("class") == "core":
                item["class"] = "Row"

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8381)