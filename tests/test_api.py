import pytest
import os
from fastapi.testclient import <PERSON><PERSON><PERSON>
from httpx import Async<PERSON>lient
import pytest_asyncio
from main_async import app

@pytest_asyncio.fixture
async def async_client():
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.mark.asyncio
async def test_basic_image_processing(async_client, mock_aiohttp_client, mock_image_processing, tmp_path):
    """Test basic image processing endpoint with mock data"""
    # Create a temporary test file
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": 0.0,
            "depth_to": 10.0,
            "segment_flag": False
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 200
    json_response = response.json()
    assert "detection" in json_response
    assert "segmentation" in json_response

@pytest.mark.asyncio
async def test_segmentation_processing(async_client, mock_aiohttp_client, mock_image_processing, tmp_path):
    """Test image processing with segmentation flag enabled"""
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": 0.0,
            "depth_to": 10.0,
            "segment_flag": True
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 200
    json_response = response.json()
    assert "segmentation" in json_response

@pytest.mark.asyncio
async def test_custom_prompt_processing(async_client, mock_aiohttp_client, mock_image_processing, tmp_path):
    """Test image processing with custom prompt"""
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": 0.0,
            "depth_to": 10.0,
            "prompt": "Custom test prompt",
            "segment_flag": False
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_invalid_depth_range(async_client, tmp_path):
    """Test handling of invalid depth range values"""
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": "invalid",
            "depth_to": 10.0,
            "segment_flag": False
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 422  # FastAPI validation error

@pytest.mark.asyncio
async def test_missing_file(async_client):
    """Test handling of missing file in request"""
    data = {
        "depth_from": 0.0,
        "depth_to": 10.0,
        "segment_flag": False
    }
    response = await async_client.post("/process", data=data)
    
    assert response.status_code == 422  # FastAPI validation error

@pytest.mark.asyncio
async def test_api_error_handling(async_client, mock_aiohttp_client, mock_image_processing, tmp_path):
    """Test handling of API errors"""
    # Modify mock to simulate API error
    mock_aiohttp_client.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value.status = 500
    
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": 0.0,
            "depth_to": 10.0,
            "segment_flag": False
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 200
    assert response.json() == {}

@pytest.mark.integration
@pytest.mark.asyncio
async def test_full_integration(async_client, tmp_path):
    """
    Integration test using real API calls.
    Requires ROBOFLOW_API_KEY environment variable.
    """
    if "ROBOFLOW_API_KEY" not in os.environ:
        pytest.skip("ROBOFLOW_API_KEY not set")
    
    test_file = tmp_path / "test.jpg"
    # Create a real test image for integration testing
    import numpy as np
    from PIL import Image
    
    # Create a simple test image
    img_array = np.zeros((100, 100, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    img.save(test_file)
    
    with open(test_file, "rb") as f:
        files = {"file": ("test.jpg", f, "image/jpeg")}
        data = {
            "depth_from": 0.0,
            "depth_to": 10.0,
            "segment_flag": False
        }
        response = await async_client.post("/process", files=files, data=data)
    
    assert response.status_code == 200
    json_response = response.json()
    # Basic structure validation
    assert isinstance(json_response, dict)