"""Unit tests for core utilities."""

import pytest
from unittest.mock import Mock, patch
from flask import Flask, request

from core.exceptions import (
    AibaseMLException, 
    RoboflowAPIError, 
    GoogleVisionError, 
    GeminiAPIError,
    ImageProcessingError,
    AzureStorageError
)
from core.response import (
    SuccessResponse, 
    ErrorResponse, 
    create_success_response, 
    create_error_response
)
from core.decorators import handle_errors, validate_file_upload, validate_numeric_params


class TestExceptions:
    """Test cases for custom exceptions."""
    
    def test_aibase_ml_exception_basic(self):
        """Test basic AibaseMLException functionality."""
        error = AibaseMLException("Test error")
        
        assert str(error) == "Test error"
        assert error.message == "Test error"
        assert error.error_code == "AIBASE_ML_ERROR"
        assert error.details == {}
    
    def test_aibase_ml_exception_with_details(self):
        """Test AibaseMLException with details and custom error code."""
        details = {"param": "value", "count": 42}
        error = AibaseMLException("Test error", error_code="CUSTOM_ERROR", details=details)
        
        assert error.message == "Test error"
        assert error.error_code == "CUSTOM_ERROR"
        assert error.details == details
    
    def test_roboflow_api_error(self):
        """Test RoboflowAPIError specific functionality."""
        error = RoboflowAPIError("Model inference failed")
        
        assert error.error_code == "ROBOFLOW_API_ERROR"
        assert "Model inference failed" in str(error)
    
    def test_google_vision_error(self):
        """Test GoogleVisionError specific functionality."""
        error = GoogleVisionError("OCR failed")
        
        assert error.error_code == "GOOGLE_VISION_ERROR"
        assert "OCR failed" in str(error)
    
    def test_gemini_api_error(self):
        """Test GeminiAPIError specific functionality."""
        error = GeminiAPIError("AI processing failed")
        
        assert error.error_code == "GEMINI_API_ERROR"
        assert "AI processing failed" in str(error)
    
    def test_image_processing_error(self):
        """Test ImageProcessingError specific functionality."""
        error = ImageProcessingError("Image transformation failed")
        
        assert error.error_code == "IMAGE_PROCESSING_ERROR"
        assert "Image transformation failed" in str(error)
    
    def test_azure_storage_error(self):
        """Test AzureStorageError specific functionality."""
        error = AzureStorageError("Upload failed")
        
        assert error.error_code == "AZURE_STORAGE_ERROR"
        assert "Upload failed" in str(error)


class TestResponse:
    """Test cases for response handling."""
    
    def test_success_response_creation(self):
        """Test SuccessResponse creation."""
        data = {"result": "success", "count": 5}
        response = SuccessResponse(data=data)
        
        assert response.success is True
        assert response.data == data
        assert response.error is None
        assert response.error_code is None
    
    def test_error_response_creation(self):
        """Test ErrorResponse creation."""
        error_msg = "Something went wrong"
        error_code = "TEST_ERROR"
        details = {"field": "value"}
        
        response = ErrorResponse(
            error=error_msg,
            error_code=error_code,
            details=details
        )
        
        assert response.success is False
        assert response.data is None
        assert response.error == error_msg
        assert response.error_code == error_code
        assert response.details == details
    
    def test_create_success_response_function(self):
        """Test create_success_response helper function."""
        data = {"test": "data"}
        flask_response = create_success_response(data)
        
        assert flask_response.status_code == 200
        assert flask_response.is_json
        
        json_data = flask_response.get_json()
        assert json_data["success"] is True
        assert json_data["data"] == data
    
    def test_create_error_response_function(self):
        """Test create_error_response helper function."""
        error_msg = "Test error"
        details = {"param": "invalid"}
        
        flask_response = create_error_response(
            error_msg, 
            details=details, 
            status_code=400
        )
        
        assert flask_response.status_code == 400
        assert flask_response.is_json
        
        json_data = flask_response.get_json()
        assert json_data["success"] is False
        assert json_data["error"] == error_msg
        assert json_data["details"] == details


class TestDecorators:
    """Test cases for decorators."""
    
    def test_handle_errors_decorator_success(self):
        """Test handle_errors decorator with successful function."""
        app = Flask(__name__)
        
        @handle_errors
        def test_function():
            return create_success_response({"result": "ok"})
        
        with app.app_context():
            response = test_function()
            assert response.status_code == 200
    
    def test_handle_errors_decorator_with_exception(self):
        """Test handle_errors decorator with exception."""
        app = Flask(__name__)
        
        @handle_errors
        def test_function():
            raise ValueError("Test error")
        
        with app.app_context():
            response = test_function()
            assert response.status_code == 500
            json_data = response.get_json()
            assert json_data["success"] is False
            assert "Test error" in json_data["error"]
    
    def test_handle_errors_decorator_with_custom_exception(self):
        """Test handle_errors decorator with custom AibaseMLException."""
        app = Flask(__name__)
        
        @handle_errors
        def test_function():
            raise RoboflowAPIError("Model failed", details={"model": "test"})
        
        with app.app_context():
            response = test_function()
            assert response.status_code == 500
            json_data = response.get_json()
            assert json_data["success"] is False
            assert json_data["error_code"] == "ROBOFLOW_API_ERROR"
            assert json_data["details"]["model"] == "test"
    
    def test_validate_file_upload_decorator_success(self):
        """Test validate_file_upload decorator with valid file."""
        app = Flask(__name__)
        
        @validate_file_upload()
        def test_function():
            return create_success_response({"result": "ok"})
        
        with app.test_request_context('/test', method='POST', 
                                    data={'file': (Mock(), 'test.jpg')}):
            # Mock the file upload
            request.files = {'file': Mock()}
            request.files['file'].filename = 'test.jpg'
            
            response = test_function()
            assert response.status_code == 200
    
    def test_validate_file_upload_decorator_missing_file(self):
        """Test validate_file_upload decorator with missing file."""
        app = Flask(__name__)
        
        @validate_file_upload()
        def test_function():
            return create_success_response({"result": "ok"})
        
        with app.test_request_context('/test', method='POST'):
            request.files = {}
            
            response = test_function()
            assert response.status_code == 400
            json_data = response.get_json()
            assert json_data["success"] is False
            assert "No file uploaded" in json_data["error"]
    
    def test_validate_numeric_params_decorator_success(self):
        """Test validate_numeric_params decorator with valid parameters."""
        app = Flask(__name__)
        
        @validate_numeric_params(['depth_from', 'depth_to'])
        def test_function():
            return create_success_response({"result": "ok"})
        
        with app.test_request_context('/test', method='POST', 
                                    data={'depth_from': '1.5', 'depth_to': '2.0'}):
            response = test_function()
            assert response.status_code == 200
    
    def test_validate_numeric_params_decorator_invalid(self):
        """Test validate_numeric_params decorator with invalid parameters."""
        app = Flask(__name__)
        
        @validate_numeric_params(['depth_from'])
        def test_function():
            return create_success_response({"result": "ok"})
        
        with app.test_request_context('/test', method='POST', 
                                    data={'depth_from': 'invalid'}):
            response = test_function()
            assert response.status_code == 400
            json_data = response.get_json()
            assert json_data["success"] is False
            assert "must be numeric" in json_data["error"]
