"""Unit tests for configuration management."""

import pytest
import os
from unittest.mock import patch

from config.settings import Settings, get_settings
from config.logging import get_logger


class TestSettings:
    """Test cases for Settings configuration."""
    
    def test_settings_default_values(self):
        """Test that settings have expected default values."""
        settings = Settings()
        
        # Check that required API keys have default values
        assert settings.roboflow_api_key == "hM2hVNK6d4UFxGMXjXkT"
        assert settings.google_vision_api_key == "AIzaSyDCnITbjPc9xQckAz3_xD37v0-v2ScAUTc"
        assert settings.gemini_api_key == "AIzaSyDCnITbjPc9xQckAz3_xD37v0-v2ScAUTc"
        
        # Check Azure settings
        assert "aibaseimagestorage" in settings.azure_storage_connection_string
        assert settings.azure_container_name == "images-stag/warped_image"
        
        # Check model configurations
        assert settings.box_core_block_model_id == "box-core-block-segmentation/7"
        assert settings.box_core_block_model_id_v6 == "box-core-block-segmentation/6"
        
        # Check processing parameters
        assert settings.confidence_threshold == 0.5
        assert settings.y_tolerance == 50
        assert settings.padding == 10
    
    @patch.dict(os.environ, {
        'ROBOFLOW_API_KEY': 'test_roboflow_key',
        'GOOGLE_VISION_API_KEY': 'test_google_key',
        'GEMINI_API_KEY': 'test_gemini_key',
        'AZURE_STORAGE_CONNECTION_STRING': 'test_azure_connection',
        'AZURE_CONTAINER_NAME': 'test_container',
        'CONFIDENCE_THRESHOLD': '0.7'
    })
    def test_settings_environment_override(self):
        """Test that environment variables override default values."""
        settings = Settings()
        
        assert settings.roboflow_api_key == 'test_roboflow_key'
        assert settings.google_vision_api_key == 'test_google_key'
        assert settings.gemini_api_key == 'test_gemini_key'
        assert settings.azure_storage_connection_string == 'test_azure_connection'
        assert settings.azure_container_name == 'test_container'
        assert settings.confidence_threshold == 0.7
    
    def test_get_settings_singleton(self):
        """Test that get_settings returns the same instance."""
        settings1 = get_settings()
        settings2 = get_settings()
        
        assert settings1 is settings2
    
    def test_settings_validation(self):
        """Test settings validation for required fields."""
        settings = Settings()
        
        # Check that API keys are not empty
        assert len(settings.roboflow_api_key) > 0
        assert len(settings.google_vision_api_key) > 0
        assert len(settings.gemini_api_key) > 0
        assert len(settings.azure_storage_connection_string) > 0
        
        # Check that numeric values are valid
        assert 0 <= settings.confidence_threshold <= 1
        assert settings.y_tolerance >= 0
        assert settings.padding >= 0


class TestLogging:
    """Test cases for logging configuration."""
    
    def test_get_logger_returns_logger(self):
        """Test that get_logger returns a valid logger instance."""
        logger = get_logger(__name__)
        
        assert logger is not None
        assert hasattr(logger, 'info')
        assert hasattr(logger, 'error')
        assert hasattr(logger, 'warning')
        assert hasattr(logger, 'debug')
    
    def test_logger_name_setting(self):
        """Test that logger name is set correctly."""
        test_name = "test.module"
        logger = get_logger(test_name)
        
        assert logger.name == test_name
    
    def test_multiple_loggers_different_names(self):
        """Test that different logger names create different loggers."""
        logger1 = get_logger("module1")
        logger2 = get_logger("module2")
        
        assert logger1.name != logger2.name
        assert logger1.name == "module1"
        assert logger2.name == "module2"
    
    def test_logger_level_configuration(self):
        """Test that logger level can be configured."""
        logger = get_logger(__name__)
        
        # Logger should have a level set
        assert hasattr(logger, 'level')
        assert logger.level is not None
