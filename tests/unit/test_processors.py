"""Unit tests for processors."""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch

from processors.transformation import TransformationProcessor
from services.image_processing import ImageProcessingService
from core.exceptions import ImageProcessingError


class TestTransformationProcessor:
    """Test cases for TransformationProcessor."""
    
    @pytest.fixture
    def transformation_processor(self):
        """Create TransformationProcessor instance for testing."""
        return TransformationProcessor()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        return np.ones((200, 200, 3), dtype=np.uint8) * 255
    
    @pytest.fixture
    def sample_segments_with_points(self):
        """Create sample segments with points for testing."""
        return [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 80, "height": 80,
                "confidence": 0.9,
                "points": [(60, 60), (140, 60), (140, 140), (60, 140)]
            },
            {
                "class": "Core",
                "x": 100, "y": 80, "width": 40, "height": 20,
                "confidence": 0.8,
                "points": [(80, 70), (120, 70), (120, 90), (80, 90)]
            }
        ]
    
    @pytest.fixture
    def sample_segments_with_bbox(self):
        """Create sample segments with bounding boxes for testing."""
        return [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 80, "height": 80,
                "confidence": 0.9
            },
            {
                "class": "Core",
                "x": 100, "y": 80, "width": 40, "height": 20,
                "confidence": 0.8
            }
        ]
    
    def test_apply_box_transform_with_points(self, transformation_processor, sample_image, sample_segments_with_points):
        """Test perspective transformation with polygon points."""
        warped_image, transformed_segments = transformation_processor.apply_box_transform_to_segments(
            sample_segments_with_points, sample_image, extent=10
        )
        
        assert warped_image is not None
        assert warped_image.shape[2] == 3  # RGB image
        assert len(transformed_segments) == 2
        
        # Check that all segments have required fields
        for seg in transformed_segments:
            assert "class" in seg
            assert "x" in seg
            assert "y" in seg
            assert "width" in seg
            assert "height" in seg
            assert "confidence" in seg
            assert "points" in seg
    
    def test_apply_box_transform_with_bbox(self, transformation_processor, sample_image, sample_segments_with_bbox):
        """Test perspective transformation with bounding boxes."""
        warped_image, transformed_segments = transformation_processor.apply_box_transform_to_segments(
            sample_segments_with_bbox, sample_image, extent=10
        )
        
        assert warped_image is not None
        assert len(transformed_segments) == 2
        
        # Check that segments don't have points (bbox mode)
        for seg in transformed_segments:
            assert "class" in seg
            assert "x" in seg
            assert "y" in seg
            assert "width" in seg
            assert "height" in seg
            assert "confidence" in seg
            # Points should not be present in bbox mode
    
    def test_apply_box_transform_no_box_segment(self, transformation_processor, sample_image):
        """Test transformation when no box segment is found."""
        segments_without_box = [
            {
                "class": "Core",
                "x": 100, "y": 80, "width": 40, "height": 20,
                "confidence": 0.8
            }
        ]
        
        with pytest.raises(ImageProcessingError) as exc_info:
            transformation_processor.apply_box_transform_to_segments(
                segments_without_box, sample_image, extent=10
            )
        
        assert "No box segment found" in str(exc_info.value)
    
    def test_update_bounding_boxes_from_points(self, transformation_processor):
        """Test updating bounding boxes from polygon points."""
        segments = [
            {
                "class": "Core",
                "x": 50, "y": 50, "width": 20, "height": 20,  # Original bbox
                "confidence": 0.8,
                "points": [(40, 40), (80, 40), (80, 80), (40, 80)]  # Actual points
            },
            {
                "class": "Block",
                "x": 100, "y": 100, "width": 30, "height": 30,
                "confidence": 0.9
                # No points - should remain unchanged
            }
        ]
        
        updated_segments = transformation_processor.update_bounding_boxes_from_points(segments)
        
        assert len(updated_segments) == 2
        
        # First segment should have updated bbox from points
        core_seg = updated_segments[0]
        assert core_seg["x"] == 60.0  # Center of (40,40) to (80,80)
        assert core_seg["y"] == 60.0
        assert core_seg["width"] == 40.0  # 80 - 40
        assert core_seg["height"] == 40.0  # 80 - 40
        
        # Second segment should remain unchanged
        block_seg = updated_segments[1]
        assert block_seg["x"] == 100
        assert block_seg["y"] == 100
        assert block_seg["width"] == 30
        assert block_seg["height"] == 30
    
    def test_align_core_bounding_boxes_to_box_segment(self, transformation_processor):
        """Test aligning core segments to box boundaries."""
        segments = [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 100, "height": 100,
                "confidence": 0.9
            },
            {
                "class": "Core",
                "x": 80, "y": 80, "width": 30, "height": 20,
                "confidence": 0.8
            },
            {
                "class": "Core",
                "x": 120, "y": 120, "width": 25, "height": 15,
                "confidence": 0.7
            }
        ]
        
        aligned_segments = transformation_processor.align_core_bounding_boxes_to_box_segment(
            segments, padding=5, iou_threshold=0.8
        )
        
        # Should have 1 box + aligned cores
        box_segments = [seg for seg in aligned_segments if seg['class'].lower() == 'box']
        core_segments = [seg for seg in aligned_segments if seg['class'].lower() == 'core']
        
        assert len(box_segments) == 1
        assert len(core_segments) >= 1  # May remove duplicates
        
        # Check that core segments are aligned to box width
        box_left = 100 - 100/2 + 5  # x - width/2 + padding
        box_right = 100 + 100/2 - 5  # x + width/2 - padding
        expected_width = box_right - box_left
        
        for core in core_segments:
            assert abs(core["width"] - expected_width) < 1  # Allow small floating point differences
            assert abs(core["x"] - (box_left + box_right) / 2) < 1
    
    def test_align_core_bounding_boxes_no_box(self, transformation_processor):
        """Test core alignment when no box segment exists."""
        segments = [
            {
                "class": "Core",
                "x": 80, "y": 80, "width": 30, "height": 20,
                "confidence": 0.8
            }
        ]
        
        # Should return original segments when no box found
        result = transformation_processor.align_core_bounding_boxes_to_box_segment(segments)
        
        assert len(result) == 1
        assert result[0]["x"] == 80  # Unchanged
    
    def test_align_core_bounding_boxes_with_points(self, transformation_processor):
        """Test core alignment with points update."""
        segments = [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 100, "height": 100,
                "confidence": 0.9
            },
            {
                "class": "Core",
                "x": 80, "y": 80, "width": 30, "height": 20,
                "confidence": 0.8,
                "points": [(65, 70), (95, 70), (95, 90), (65, 90)]
            }
        ]
        
        aligned_segments = transformation_processor.align_core_bounding_boxes_to_box_segment(
            segments, padding=5
        )
        
        core_segments = [seg for seg in aligned_segments if seg['class'].lower() == 'core']
        
        if core_segments:
            core = core_segments[0]
            assert "points" in core
            assert len(core["points"]) == 4
            
            # Points should be updated to match new aligned bounding box
            points = core["points"]
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            
            # All x coordinates should be at the aligned positions
            assert len(set(x_coords)) == 2  # Should have left and right edges
    
    def test_iou_computation_in_alignment(self, transformation_processor):
        """Test IoU computation for duplicate removal."""
        segments = [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 100, "height": 100,
                "confidence": 0.9
            },
            {
                "class": "Core",
                "x": 80, "y": 80, "width": 30, "height": 20,
                "confidence": 0.8
            },
            {
                "class": "Core",
                "x": 82, "y": 82, "width": 28, "height": 18,  # Very similar to above
                "confidence": 0.7
            }
        ]
        
        # With high IoU threshold, should remove the duplicate
        aligned_segments = transformation_processor.align_core_bounding_boxes_to_box_segment(
            segments, iou_threshold=0.5
        )
        
        core_segments = [seg for seg in aligned_segments if seg['class'].lower() == 'core']
        
        # Should have fewer cores due to duplicate removal
        assert len(core_segments) <= 2
