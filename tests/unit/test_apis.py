"""Unit tests for API endpoints."""

import pytest
import json
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from io import BytesIO

from apis.main_processing import create_main_processing_app
from apis.segment_auto_crop import create_segment_auto_crop_app
from apis.segment_core_pieces import create_segment_core_pieces_app
from apis.segment_core_server import create_segment_core_server_app


class TestMainProcessingAPI:
    """Test cases for Main Processing API."""
    
    @pytest.fixture
    def app(self):
        """Create test app instance."""
        app = create_main_processing_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def sample_image_file(self):
        """Create a sample image file for testing."""
        # Create a simple test image
        image_data = np.ones((100, 100, 3), dtype=np.uint8) * 255
        import cv2
        _, buffer = cv2.imencode('.jpg', image_data)
        return BytesIO(buffer.tobytes())
    
    def test_home_endpoint(self, client):
        """Test home endpoint returns API documentation."""
        response = client.get('/')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['service'] == 'Main Processing API'
        assert 'endpoints' in data
        assert '/process' in data['endpoints']
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get('/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['status'] == 'healthy'
    
    @patch('apis.main_processing.RoboflowService')
    @patch('apis.main_processing.AzureStorageService')
    @patch('apis.main_processing.GoogleVisionService')
    @patch('apis.main_processing.GeminiService')
    @patch('apis.main_processing.ImageProcessingService')
    @patch('apis.main_processing.TransformationProcessor')
    def test_process_endpoint_success(self, mock_transform, mock_image_service, 
                                    mock_gemini, mock_google, mock_azure, 
                                    mock_roboflow, client, sample_image_file):
        """Test successful processing endpoint."""
        # Mock all service responses
        mock_roboflow_instance = Mock()
        mock_roboflow_instance.run_box_core_block_segmentation.return_value = (
            "Detection", {"predictions": []}, 0.5
        )
        mock_roboflow_instance.transform_segmentation_format.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_roboflow.return_value = mock_roboflow_instance
        
        mock_image_service_instance = Mock()
        mock_image_service_instance.merge_boxes_as_one_segment.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_image_service_instance.filter_segments_inside_box.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_image_service_instance.crop_boxes_from_segments.return_value = [np.ones((50, 50, 3), dtype=np.uint8)]
        mock_image_service.return_value = mock_image_service_instance
        
        mock_azure_instance = Mock()
        mock_azure_instance.upload_warped_image.return_value = {
            "success": True, "url": "test_url", "filename": "test.jpg"
        }
        mock_azure.return_value = mock_azure_instance
        
        mock_google_instance = Mock()
        mock_google_instance.run_ocr_on_cropped_images.return_value = ("Google OCR", ["Sample text"], 0.3)
        mock_google.return_value = mock_google_instance
        
        mock_gemini_instance = Mock()
        mock_gemini_instance.run_depth_extraction.return_value = ("Gemini", ["1.5"], 0.4)
        mock_gemini.return_value = mock_gemini_instance
        
        mock_transform_instance = Mock()
        mock_transform_instance.apply_box_transform_to_segments.return_value = (
            np.ones((100, 100, 3), dtype=np.uint8), 
            [{"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}]
        )
        mock_transform_instance.update_bounding_boxes_from_points.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_transform_instance.align_core_bounding_boxes_to_box_segment.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_transform.return_value = mock_transform_instance
        
        # Test the endpoint
        response = client.post('/process', data={
            'image': (sample_image_file, 'test.jpg'),
            'use_segmentation': 'true',
            'depth_from': '0.0',
            'depth_to': '2.4'
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'warped_image' in data['data']
        assert 'google_result' in data['data']
        assert 'gemini_result' in data['data']
    
    def test_process_endpoint_missing_file(self, client):
        """Test process endpoint with missing file."""
        response = client.post('/process', data={})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'No file uploaded' in data['error']
    
    def test_process_endpoint_invalid_image(self, client):
        """Test process endpoint with invalid image file."""
        invalid_file = BytesIO(b'not an image')
        
        response = client.post('/process', data={
            'image': (invalid_file, 'test.txt')
        })
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False


class TestSegmentAutoCropAPI:
    """Test cases for Segment Auto Crop API."""
    
    @pytest.fixture
    def app(self):
        """Create test app instance."""
        app = create_segment_auto_crop_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def test_home_endpoint(self, client):
        """Test home endpoint returns API documentation."""
        response = client.get('/')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['service'] == 'Segment Auto Crop API'
        assert '/segment_crop' in data['endpoints']
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get('/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['service'] == 'segment_auto_crop'


class TestSegmentCorePiecesAPI:
    """Test cases for Segment Core Pieces API."""
    
    @pytest.fixture
    def app(self):
        """Create test app instance."""
        app = create_segment_core_pieces_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def test_home_endpoint(self, client):
        """Test home endpoint returns API documentation."""
        response = client.get('/')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['service'] == 'Segment Core Pieces API'
        assert '/segment_core_pieces' in data['endpoints']
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get('/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['service'] == 'segment_core_pieces'


class TestSegmentCoreServerAPI:
    """Test cases for Segment Core Server API."""
    
    @pytest.fixture
    def app(self):
        """Create test app instance."""
        app = create_segment_core_server_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    def test_home_endpoint(self, client):
        """Test home endpoint returns API documentation."""
        response = client.get('/')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['service'] == 'Segment Core Server API'
        assert '/process_core_outline' in data['endpoints']
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get('/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['service'] == 'segment_core_server'
    
    @patch('apis.segment_core_server.RoboflowService')
    @patch('apis.segment_core_server.AzureStorageService')
    @patch('apis.segment_core_server.GoogleVisionService')
    @patch('apis.segment_core_server.GeminiService')
    @patch('apis.segment_core_server.ImageProcessingService')
    @patch('apis.segment_core_server.TransformationProcessor')
    def test_process_core_outline_success(self, mock_transform, mock_image_service,
                                        mock_gemini, mock_google, mock_azure,
                                        mock_roboflow, client):
        """Test successful core outline processing."""
        # Mock all service responses
        mock_roboflow_instance = Mock()
        mock_roboflow_instance.run_box_core_block_segmentation.return_value = (
            "Detection", {"predictions": []}, 0.5
        )
        mock_roboflow_instance.transform_segmentation_format.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_roboflow.return_value = mock_roboflow_instance
        
        mock_image_service_instance = Mock()
        mock_image_service_instance.merge_boxes_as_one_segment.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_image_service_instance.filter_segments_inside_box.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_image_service_instance.crop_boxes_from_segments.return_value = [np.ones((50, 50, 3), dtype=np.uint8)]
        mock_image_service.return_value = mock_image_service_instance
        
        mock_azure_instance = Mock()
        mock_azure_instance.upload_warped_image.return_value = {
            "success": True, "url": "test_url", "filename": "test.jpg"
        }
        mock_azure.return_value = mock_azure_instance
        
        mock_google_instance = Mock()
        mock_google_instance.run_ocr_on_cropped_images.return_value = ("Google OCR", ["Sample text"], 0.3)
        mock_google.return_value = mock_google_instance
        
        mock_gemini_instance = Mock()
        mock_gemini_instance.run_depth_extraction.return_value = ("Gemini", ["1.5"], 0.4)
        mock_gemini.return_value = mock_gemini_instance
        
        mock_transform_instance = Mock()
        mock_transform_instance.apply_box_transform_to_segments.return_value = (
            np.ones((100, 100, 3), dtype=np.uint8),
            [{"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}]
        )
        mock_transform_instance.update_bounding_boxes_from_points.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_transform_instance.align_core_bounding_boxes_to_box_segment.return_value = [
            {"class": "Block", "x": 100, "y": 100, "width": 50, "height": 50, "confidence": 0.8}
        ]
        mock_transform.return_value = mock_transform_instance
        
        # Create sample image file
        image_data = np.ones((100, 100, 3), dtype=np.uint8) * 255
        import cv2
        _, buffer = cv2.imencode('.jpg', image_data)
        sample_image_file = BytesIO(buffer.tobytes())
        
        # Test the endpoint
        response = client.post('/process_core_outline', data={
            'image': (sample_image_file, 'test.jpg'),
            'use_row': 'true',
            'depth_from': '0.0',
            'depth_to': '2.4'
        })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'blocks' in data['data']
        assert 'processing_info' in data['data']
