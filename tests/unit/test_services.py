"""Unit tests for services layer."""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch, MagicMock
from io import BytesIO

from services.roboflow_service import RoboflowService
from services.google_vision import GoogleVisionService
from services.gemini_service import GeminiService
from services.image_processing import ImageProcessingService
from services.azure_storage import AzureStorageService
from core.exceptions import (
    RoboflowAPIError, 
    GoogleVisionError, 
    GeminiAPIError,
    ImageProcessingError,
    AzureStorageError
)


class TestRoboflowService:
    """Test cases for RoboflowService."""
    
    @pytest.fixture
    def roboflow_service(self):
        """Create RoboflowService instance for testing."""
        return RoboflowService()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        return np.ones((100, 100, 3), dtype=np.uint8) * 255
    
    @patch('services.roboflow_service.get_roboflow_model')
    def test_run_box_core_block_segmentation_success(self, mock_get_model, roboflow_service, sample_image):
        """Test successful segmentation."""
        # Mock the model and result
        mock_model = Mock()
        mock_result = Mock()
        mock_result.dict.return_value = {"predictions": []}
        mock_model.infer.return_value = [mock_result]
        mock_get_model.return_value = mock_model
        
        operation, result, elapsed_time = roboflow_service.run_box_core_block_segmentation(sample_image)
        
        assert operation == "Detection"
        assert isinstance(result, dict)
        assert elapsed_time >= 0
        mock_model.infer.assert_called_once_with(sample_image)
    
    @patch('services.roboflow_service.get_roboflow_model')
    def test_run_box_core_block_segmentation_model_error(self, mock_get_model, roboflow_service, sample_image):
        """Test segmentation with model loading error."""
        mock_get_model.side_effect = Exception("Model loading failed")
        
        with pytest.raises(RoboflowAPIError) as exc_info:
            roboflow_service.run_box_core_block_segmentation(sample_image)
        
        assert "Failed to load Roboflow model" in str(exc_info.value)
    
    def test_transform_segmentation_format(self, roboflow_service):
        """Test segmentation format transformation."""
        segmentation_data = {
            "predictions": [
                {
                    "class_name": "Block",
                    "x": 100,
                    "y": 200,
                    "width": 50,
                    "height": 30,
                    "confidence": 0.8,
                    "points": [{"x": 75, "y": 185}, {"x": 125, "y": 215}]
                }
            ]
        }
        
        result = roboflow_service.transform_segmentation_format(segmentation_data)
        
        assert len(result) == 1
        segment = result[0]
        assert segment["class"] == "Block"
        assert segment["x"] == 100.0
        assert segment["y"] == 200.0
        assert segment["confidence"] == 0.8
        assert "points" in segment
    
    def test_transform_segmentation_format_confidence_filter(self, roboflow_service):
        """Test segmentation format transformation with confidence filtering."""
        segmentation_data = {
            "predictions": [
                {"class_name": "Block", "x": 100, "y": 200, "width": 50, "height": 30, "confidence": 0.8},
                {"class_name": "Block", "x": 150, "y": 250, "width": 50, "height": 30, "confidence": 0.3}
            ]
        }
        
        result = roboflow_service.transform_segmentation_format(
            segmentation_data, 
            confidence_threshold=0.5
        )
        
        assert len(result) == 1
        assert result[0]["confidence"] == 0.8
    
    def test_clear_model_cache(self, roboflow_service):
        """Test model cache clearing."""
        # Add some mock models to cache
        roboflow_service._models["test_model"] = Mock()
        
        assert len(roboflow_service._models) > 0
        
        roboflow_service.clear_model_cache()
        
        assert len(roboflow_service._models) == 0


class TestGoogleVisionService:
    """Test cases for GoogleVisionService."""
    
    @pytest.fixture
    def google_service(self):
        """Create GoogleVisionService instance for testing."""
        return GoogleVisionService()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        return np.ones((100, 100, 3), dtype=np.uint8) * 255
    
    @patch('services.google_vision.requests.post')
    def test_run_ocr_on_image_success(self, mock_post, google_service, sample_image):
        """Test successful OCR operation."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "responses": [{
                "fullTextAnnotation": {
                    "text": "Sample text"
                }
            }]
        }
        mock_post.return_value = mock_response
        
        result = google_service.run_ocr_on_image(sample_image)
        
        assert result == "Sample text"
        mock_post.assert_called_once()
    
    @patch('services.google_vision.requests.post')
    def test_run_ocr_on_image_api_error(self, mock_post, google_service, sample_image):
        """Test OCR with API error."""
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Bad request"
        mock_post.return_value = mock_response
        
        with pytest.raises(GoogleVisionError) as exc_info:
            google_service.run_ocr_on_image(sample_image)
        
        assert "Google Vision API request failed" in str(exc_info.value)
    
    @patch('services.google_vision.cv2.imencode')
    def test_run_ocr_on_image_encode_error(self, mock_imencode, google_service, sample_image):
        """Test OCR with image encoding error."""
        mock_imencode.return_value = (False, None)
        
        with pytest.raises(GoogleVisionError) as exc_info:
            google_service.run_ocr_on_image(sample_image)
        
        assert "Failed to encode image" in str(exc_info.value)
    
    def test_run_ocr_on_cropped_images(self, google_service):
        """Test OCR on multiple cropped images."""
        sample_images = [
            np.ones((50, 50, 3), dtype=np.uint8) * 255,
            np.ones((60, 60, 3), dtype=np.uint8) * 128
        ]
        
        with patch.object(google_service, 'run_ocr_on_image') as mock_ocr:
            mock_ocr.side_effect = ["Text 1", "Text 2"]
            
            operation, results, elapsed_time = google_service.run_ocr_on_cropped_images(sample_images)
            
            assert operation == "Google OCR"
            assert len(results) == 2
            assert results[0] == "Text 1"
            assert results[1] == "Text 2"
            assert elapsed_time >= 0


class TestGeminiService:
    """Test cases for GeminiService."""
    
    @pytest.fixture
    def gemini_service(self):
        """Create GeminiService instance for testing."""
        return GeminiService()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        return np.ones((100, 100, 3), dtype=np.uint8) * 255
    
    def test_generate_depth_extraction_prompt(self, gemini_service):
        """Test depth extraction prompt generation."""
        prompt = gemini_service.generate_depth_extraction_prompt(0.5, 2.0)
        
        assert "0.5 to 2.0" in prompt
        assert "HD" in prompt
        assert "depth" in prompt.lower()
    
    @patch('services.gemini_service.genai.Client')
    def test_run_prompt_on_image_success(self, mock_client_class, gemini_service, sample_image):
        """Test successful Gemini processing."""
        # Mock the client and response
        mock_client = Mock()
        mock_response = Mock()
        mock_response.text = "Extracted text"
        mock_client.models.generate_content.return_value = mock_response
        mock_client_class.return_value = mock_client
        
        result = gemini_service.run_prompt_on_image(sample_image, "Test prompt")
        
        assert result == "Extracted text"
        mock_client.models.generate_content.assert_called_once()
    
    @patch('services.gemini_service.genai.Client')
    def test_run_prompt_on_image_client_error(self, mock_client_class, gemini_service, sample_image):
        """Test Gemini processing with client error."""
        mock_client_class.side_effect = Exception("Client initialization failed")
        
        with pytest.raises(GeminiAPIError) as exc_info:
            gemini_service.run_prompt_on_image(sample_image, "Test prompt")
        
        assert "Failed to initialize Gemini client" in str(exc_info.value)
    
    def test_run_depth_extraction(self, gemini_service):
        """Test depth extraction functionality."""
        sample_images = [np.ones((50, 50, 3), dtype=np.uint8) * 255]
        
        with patch.object(gemini_service, 'run_prompt_on_cropped_images') as mock_run:
            mock_run.return_value = ("Gemini", ["1.5"], 0.5)
            
            operation, results, elapsed_time = gemini_service.run_depth_extraction(
                sample_images, 0.0, 3.0
            )
            
            assert operation == "Gemini"
            assert len(results) == 1
            assert results[0] == "1.5"


class TestImageProcessingService:
    """Test cases for ImageProcessingService."""
    
    @pytest.fixture
    def image_service(self):
        """Create ImageProcessingService instance for testing."""
        return ImageProcessingService()
    
    def test_scale_contour(self, image_service):
        """Test contour scaling."""
        contour = np.array([[10, 10], [20, 10], [20, 20], [10, 20]], dtype=np.int32)
        scaled = image_service.scale_contour(contour, 2.0)
        
        assert scaled.shape == contour.shape
        assert scaled.dtype == np.int32
    
    def test_merge_boxes_as_one_segment(self, image_service):
        """Test box merging functionality."""
        segments = [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 50, "height": 50,
                "confidence": 0.8,
                "points": [(75, 75), (125, 75), (125, 125), (75, 125)]
            },
            {
                "class": "Box", 
                "x": 150, "y": 150, "width": 50, "height": 50,
                "confidence": 0.9,
                "points": [(125, 125), (175, 125), (175, 175), (125, 175)]
            },
            {
                "class": "Core",
                "x": 120, "y": 120, "width": 30, "height": 30,
                "confidence": 0.7
            }
        ]
        
        result = image_service.merge_boxes_as_one_segment(segments)
        
        # Should have 2 segments: 1 merged box + 1 core
        assert len(result) == 2
        
        # Find the merged box
        merged_box = next(seg for seg in result if seg['class'] == 'Box')
        assert merged_box['confidence'] == 0.85  # Average of 0.8 and 0.9
        assert 'points' in merged_box
    
    def test_filter_segments_inside_box(self, image_service):
        """Test segment filtering inside box."""
        segments = [
            {
                "class": "Box",
                "x": 100, "y": 100, "width": 100, "height": 100,
                "confidence": 0.8,
                "points": [(50, 50), (150, 50), (150, 150), (50, 150)]
            },
            {
                "class": "Core",
                "x": 100, "y": 100, "width": 30, "height": 30,  # Inside box
                "confidence": 0.7
            },
            {
                "class": "Core",
                "x": 200, "y": 200, "width": 30, "height": 30,  # Outside box
                "confidence": 0.6
            }
        ]
        
        box_segment = segments[0]
        result = image_service.filter_segments_inside_box(segments, box_segment)
        
        # Should include box + 1 core inside
        assert len(result) == 2
        core_segments = [seg for seg in result if seg['class'] == 'Core']
        assert len(core_segments) == 1
        assert core_segments[0]['x'] == 100  # The inside core


class TestAzureStorageService:
    """Test cases for AzureStorageService."""
    
    @pytest.fixture
    def azure_service(self):
        """Create AzureStorageService instance for testing."""
        return AzureStorageService()
    
    @pytest.fixture
    def sample_image(self):
        """Create a sample image for testing."""
        return np.ones((100, 100, 3), dtype=np.uint8) * 255
    
    @patch('services.azure_storage.BlobServiceClient')
    def test_upload_image_success(self, mock_blob_client_class, azure_service, sample_image):
        """Test successful image upload."""
        # Mock the blob service client
        mock_blob_service = Mock()
        mock_blob_client = Mock()
        mock_blob_service.get_blob_client.return_value = mock_blob_client
        mock_blob_client_class.from_connection_string.return_value = mock_blob_service
        
        result = azure_service.upload_image(sample_image, "test.jpg")
        
        assert result["success"] is True
        assert "url" in result
        assert result["filename"] == "test.jpg"
        mock_blob_client.upload_blob.assert_called_once()
    
    @patch('services.azure_storage.BlobServiceClient')
    def test_upload_image_error(self, mock_blob_client_class, azure_service, sample_image):
        """Test image upload with error."""
        mock_blob_client_class.from_connection_string.side_effect = Exception("Connection failed")
        
        result = azure_service.upload_image(sample_image, "test.jpg")
        
        assert result["success"] is False
        assert "error" in result
        assert "Connection failed" in result["error"]
    
    def test_generate_filename(self, azure_service):
        """Test filename generation."""
        filename = azure_service._generate_filename()
        
        assert filename.endswith(".jpg")
        assert len(filename) > 10  # Should have UUID + extension
    
    def test_upload_warped_image(self, azure_service, sample_image):
        """Test warped image upload."""
        with patch.object(azure_service, 'upload_image') as mock_upload:
            mock_upload.return_value = {"success": True, "url": "test_url", "filename": "test.jpg"}
            
            result = azure_service.upload_warped_image(sample_image, "warped.jpg")
            
            assert result["success"] is True
            mock_upload.assert_called_once_with(sample_image, "warped.jpg")
