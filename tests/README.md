# Testing Documentation for aibase-ml

This directory contains comprehensive unit tests for the refactored aibase-ml application.

## Test Structure

```
tests/
├── unit/                    # Unit tests for individual components
│   ├── test_config.py      # Configuration and logging tests
│   ├── test_core.py        # Core utilities, exceptions, decorators
│   ├── test_services.py    # Service layer tests
│   ├── test_processors.py  # Processor tests
│   └── test_apis.py        # API endpoint tests
├── conftest.py             # Shared fixtures and configuration
├── requirements.txt        # Testing dependencies
├── run_tests.py           # Test runner script
└── README.md              # This file
```

## Running Tests

### Prerequisites

Install test dependencies:
```bash
pip install -r tests/requirements.txt
```

Or check if dependencies are installed:
```bash
python tests/run_tests.py --check-deps
```

### Basic Test Execution

Run all unit tests:
```bash
python tests/run_tests.py --type unit
```

Run with verbose output:
```bash
python tests/run_tests.py --type unit -v
```

Run with coverage reporting:
```bash
python tests/run_tests.py --type unit --coverage
```

### Using pytest directly

Run all unit tests:
```bash
pytest tests/unit/
```

Run specific test file:
```bash
pytest tests/unit/test_services.py
```

Run specific test class:
```bash
pytest tests/unit/test_services.py::TestRoboflowService
```

Run specific test method:
```bash
pytest tests/unit/test_services.py::TestRoboflowService::test_run_box_core_block_segmentation_success
```

Run tests with markers:
```bash
pytest -m unit
pytest -m api
```

## Test Categories

### Unit Tests (`tests/unit/`)

#### Configuration Tests (`test_config.py`)
- Settings validation and environment variable override
- Logging configuration and logger creation
- Singleton pattern verification

#### Core Tests (`test_core.py`)
- Custom exception hierarchy testing
- Response handling (success/error responses)
- Decorator functionality (error handling, validation)

#### Service Tests (`test_services.py`)
- **RoboflowService**: Model inference, segmentation transformation, caching
- **GoogleVisionService**: OCR operations, API error handling
- **GeminiService**: AI text processing, depth extraction
- **ImageProcessingService**: Image operations, segment filtering, box merging
- **AzureStorageService**: Blob storage operations, upload functionality

#### Processor Tests (`test_processors.py`)
- **TransformationProcessor**: Perspective transformations, segment alignment
- Bounding box updates from polygon points
- IoU computation and duplicate removal

#### API Tests (`test_apis.py`)
- All 4 API endpoints (main_processing, segment_auto_crop, segment_core_pieces, segment_core_server)
- Request/response validation
- Error handling and status codes
- Service integration testing

## Test Fixtures

### Shared Fixtures (in `conftest.py`)

- `sample_image`: Standard RGB test image
- `sample_image_file`: File-like object for upload testing
- `sample_segments`: Sample segmentation data
- `mock_settings`: Mock configuration settings
- `mock_roboflow_response`: Sample Roboflow API response
- `mock_aiohttp_client`: Mock HTTP client for async operations

### Mocking Strategy

Tests use comprehensive mocking to:
- Avoid external API calls (Roboflow, Google Vision, Gemini, Azure)
- Ensure tests run quickly and reliably
- Test error conditions and edge cases
- Isolate units under test

## Coverage Reporting

Generate coverage report:
```bash
python tests/run_tests.py --type unit --coverage
```

This creates:
- Terminal coverage summary
- HTML coverage report in `htmlcov/` directory

View HTML coverage:
```bash
open htmlcov/index.html  # macOS
xdg-open htmlcov/index.html  # Linux
```

## Test Best Practices

### Writing New Tests

1. **Follow naming conventions**: `test_<functionality>_<condition>`
2. **Use descriptive test names**: Clearly indicate what is being tested
3. **One assertion per test**: Focus on testing one specific behavior
4. **Use fixtures**: Leverage shared fixtures for common test data
5. **Mock external dependencies**: Don't make real API calls in unit tests

### Test Structure

```python
def test_service_method_success(self, service_fixture, sample_data):
    """Test successful operation with valid input."""
    # Arrange
    expected_result = "expected_value"

    # Act
    result = service_fixture.method(sample_data)

    # Assert
    assert result == expected_result
```

### Error Testing

```python
def test_service_method_error_condition(self, service_fixture):
    """Test error handling for invalid input."""
    with pytest.raises(CustomException) as exc_info:
        service_fixture.method(invalid_input)

    assert "expected error message" in str(exc_info.value)
```

## Continuous Integration

These tests are designed to run in CI/CD environments:

- No external dependencies (all mocked)
- Fast execution (< 30 seconds for full suite)
- Clear pass/fail indicators
- Detailed error reporting

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure project root is in Python path
2. **Missing Dependencies**: Install test requirements
3. **Mock Failures**: Check that external services are properly mocked
4. **Path Issues**: Run tests from project root directory

### Debug Mode

Run tests with maximum verbosity:
```bash
pytest tests/unit/ -vvv --tb=long
```

Run specific failing test:
```bash
pytest tests/unit/test_services.py::TestRoboflowService::test_failing_method -vvv
```

## Contributing

When adding new functionality:

1. Write tests first (TDD approach)
2. Ensure >90% code coverage for new code
3. Test both success and error conditions
4. Update this documentation if needed
5. Run full test suite before committing

## Performance

Current test suite performance:
- Unit tests: ~10-20 seconds
- Full coverage: ~30 seconds
- Individual test files: ~2-5 seconds

Target: Keep individual tests under 100ms each.

## Legacy Tests

The existing async API tests and utilities remain in the repository for reference:
- Original async endpoint tests
- Roboflow async utilities testing
- Integration test examples

These can be found in the original test files and serve as examples for integration testing with real APIs.