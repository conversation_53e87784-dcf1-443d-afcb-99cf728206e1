# Testing Documentation for Async AI Image Processing API

This document describes how to test the async API endpoints and utilities for the AI image processing service.

## Prerequisites

```bash
pip install pytest pytest-asyncio aiohttp httpx python-multipart
```

## API Endpoints

### POST /process

Tests the main image processing endpoint that handles image uploads and processing.

#### Test Cases

1. **Basic Image Processing**
```python
async def test_basic_image_processing():
    client = AsyncClient()
    files = {"file": ("test.jpg", open("test_data/test.jpg", "rb"))}
    data = {
        "depth_from": 0.0,
        "depth_to": 10.0,
        "segment_flag": False
    }
    response = await client.post("/process", files=files, data=data)
    assert response.status_code == 200
    assert "detection" in response.json()
    assert "segmentation" in response.json()
```

2. **Segmentation Flag Processing**
```python
async def test_segmentation_processing():
    client = AsyncClient()
    files = {"file": ("test.jpg", open("test_data/test.jpg", "rb"))}
    data = {
        "depth_from": 0.0,
        "depth_to": 10.0,
        "segment_flag": True
    }
    response = await client.post("/process", files=files, data=data)
    assert response.status_code == 200
    assert "segmentation" in response.json()
```

3. **Custom Prompt Processing**
```python
async def test_custom_prompt_processing():
    client = AsyncClient()
    files = {"file": ("test.jpg", open("test_data/test.jpg", "rb"))}
    data = {
        "depth_from": 0.0,
        "depth_to": 10.0,
        "prompt": "Custom prompt for testing",
        "segment_flag": False
    }
    response = await client.post("/process", files=files, data=data)
    assert response.status_code == 200
```

## Roboflow Async Utils

### Testing the Roboflow API Client

```python
async def test_roboflow_api_call():
    image_path = "test_data/test.jpg"
    result = await call_roboflow_api_async(
        image_path=image_path,
        depth_from=0.0,
        depth_to=10.0,
        segment_flag=False
    )
    assert result is not None
    assert "detection" in result
```

### Testing Response Processing

```python
def test_process_response():
    test_data = [{
        "segment_flag": True,
        "detection_flag": {"test": "data"},
        "google_vision_ocr": {"text": "test"},
        "google_gemini": {"output": "test"},
        "model_1_predictions": [{"class": "test"}]
    }]
    
    result = process_response(test_data)
    assert "detection" in result[0]
    assert "google_vision_block_ocr" in result[0]
    assert "google_gemini_block_crop" in result[0]
    assert "segmentation" in result[0]
```

## Mock Setup

Create a `conftest.py` file with the following mocks:

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.fixture
def mock_roboflow_response():
    return {
        "detection": {
            "predictions": []
        },
        "segmentation": [],
        "google_vision_block_ocr": [],
        "google_gemini_block_crop": []
    }

@pytest.fixture
def mock_aiohttp_client():
    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value.json = AsyncMock(
            return_value=[{
                "detection": {"predictions": []},
                "segmentation": []
            }]
        )
        yield mock_session
```

## Running Tests

1. Create test data directory:
```bash
mkdir -p test_data
# Add test images to test_data/
```

2. Run tests:
```bash
pytest tests/ -v --asyncio-mode=strict
```

## Test Files Structure

```
tests/
├── __init__.py
├── conftest.py
├── test_api.py
├── test_roboflow.py
└── test_data/
    └── test.jpg
```

## Error Cases to Test

1. Invalid file upload
2. Invalid depth range values
3. Network failures with Roboflow API
4. Malformed response from Roboflow API
5. Image processing errors

Example error test:
```python
async def test_invalid_depth_range():
    client = AsyncClient()
    files = {"file": ("test.jpg", open("test_data/test.jpg", "rb"))}
    data = {
        "depth_from": "invalid",
        "depth_to": 10.0,
        "segment_flag": False
    }
    response = await client.post("/process", files=files, data=data)
    assert response.status_code == 400
```

## Integration Testing

For integration testing with the actual Roboflow API:

1. Set up environment variables:
```bash
export ROBOFLOW_API_KEY="your_api_key"
```

2. Run integration tests:
```bash
pytest tests/ -v --asyncio-mode=strict -m "integration"
```

Mark integration tests with:
```python
@pytest.mark.integration
async def test_full_integration():
    # Test code here
    pass