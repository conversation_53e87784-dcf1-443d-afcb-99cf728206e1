import pytest
import numpy as np
import cv2
import os
import sys
from unittest.mock import AsyncMock, patch, Mock
from io import Bytes<PERSON>

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture
def mock_roboflow_response():
    return {
        "detection": {
            "predictions": [
                {
                    "x": 100,
                    "y": 100,
                    "width": 50,
                    "height": 50,
                    "class": "Row",
                    "detection_id": "test_id_1"
                }
            ]
        },
        "segmentation": [
            {
                "x": 100,
                "y": 100,
                "width": 50,
                "height": 50,
                "points": [(90, 90), (110, 90), (110, 110), (90, 110)],
                "detection_id": "test_id_1",
                "class": "core"
            }
        ],
        "google_vision_block_ocr": [
            {
                "text": "test text",
                "language": "en",
                "predictions": {"predictions": [{"parent_id": "test_id_1"}]}
            }
        ],
        "google_gemini_block_crop": [
            [{"output": "test output"}]
        ]
    }

@pytest.fixture
def mock_aiohttp_client():
    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = AsyncMock(
            status=200,
            json=AsyncMock(return_value=[{
                "detection": {
                    "predictions": [
                        {
                            "x": 100,
                            "y": 100,
                            "width": 50,
                            "height": 50,
                            "class": "Row"
                        }
                    ]
                },
                "segmentation": []
            }])
        )
        yield mock_session

@pytest.fixture
def mock_image_processing():
    with patch("utils.image.correct_image_orientation") as mock_orient, \
         patch("utils.image.get_resize_scale_ratio") as mock_ratio:
        mock_ratio.return_value = (1.0, 1.0)
        yield mock_orient, mock_ratio


# Additional fixtures for unit tests
@pytest.fixture
def sample_image():
    """Create a sample RGB image for testing."""
    return np.ones((100, 100, 3), dtype=np.uint8) * 255


@pytest.fixture
def sample_image_file():
    """Create a sample image file-like object for testing."""
    image_data = np.ones((100, 100, 3), dtype=np.uint8) * 255
    _, buffer = cv2.imencode('.jpg', image_data)
    return BytesIO(buffer.tobytes())


@pytest.fixture
def sample_segments():
    """Create sample segmentation data for testing."""
    return [
        {
            "class": "Box",
            "x": 100.0,
            "y": 100.0,
            "width": 80.0,
            "height": 80.0,
            "confidence": 0.9,
            "points": [(60, 60), (140, 60), (140, 140), (60, 140)]
        },
        {
            "class": "Core",
            "x": 100.0,
            "y": 80.0,
            "width": 40.0,
            "height": 20.0,
            "confidence": 0.8,
            "points": [(80, 70), (120, 70), (120, 90), (80, 90)]
        }
    ]


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock()
    settings.roboflow_api_key = "test_roboflow_key"
    settings.google_vision_api_key = "test_google_key"
    settings.gemini_api_key = "test_gemini_key"
    settings.azure_storage_connection_string = "test_azure_connection"
    settings.azure_container_name = "test_container"
    settings.box_core_block_model_id = "test_model/1"
    settings.confidence_threshold = 0.5
    settings.y_tolerance = 50
    settings.padding = 10
    return settings


# Test markers configuration
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line("markers", "integration: mark test as an integration test")
    config.addinivalue_line("markers", "api: mark test as an API test")