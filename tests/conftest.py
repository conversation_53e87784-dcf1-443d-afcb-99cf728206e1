import pytest
from unittest.mock import AsyncMock, patch

@pytest.fixture
def mock_roboflow_response():
    return {
        "detection": {
            "predictions": [
                {
                    "x": 100,
                    "y": 100,
                    "width": 50,
                    "height": 50,
                    "class": "Row",
                    "detection_id": "test_id_1"
                }
            ]
        },
        "segmentation": [
            {
                "x": 100,
                "y": 100,
                "width": 50,
                "height": 50,
                "points": [(90, 90), (110, 90), (110, 110), (90, 110)],
                "detection_id": "test_id_1",
                "class": "core"
            }
        ],
        "google_vision_block_ocr": [
            {
                "text": "test text",
                "language": "en",
                "predictions": {"predictions": [{"parent_id": "test_id_1"}]}
            }
        ],
        "google_gemini_block_crop": [
            [{"output": "test output"}]
        ]
    }

@pytest.fixture
def mock_aiohttp_client():
    with patch("aiohttp.ClientSession") as mock_session:
        mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = AsyncMock(
            status=200,
            json=AsyncMock(return_value=[{
                "detection": {
                    "predictions": [
                        {
                            "x": 100,
                            "y": 100,
                            "width": 50,
                            "height": 50,
                            "class": "Row"
                        }
                    ]
                },
                "segmentation": []
            }])
        )
        yield mock_session

@pytest.fixture
def mock_image_processing():
    with patch("utils.image.correct_image_orientation") as mock_orient, \
         patch("utils.image.get_resize_scale_ratio") as mock_ratio:
        mock_ratio.return_value = (1.0, 1.0)
        yield mock_orient, mock_ratio