#!/usr/bin/env python3
"""
Integration test runner for aibase-ml.

Runs integration tests with proper configuration and reporting.
"""

import argparse
import sys
import os
import subprocess
import time
import requests
from pathlib import Path
import json


class IntegrationTestRunner:
    """Runner for integration tests with health checks and reporting."""
    
    def __init__(self):
        self.test_dir = Path(__file__).parent / "integration"
        self.api_urls = {
            'main_processing': 'http://localhost:8386/health',
            'segment_auto_crop': 'http://localhost:8388/health',
            'segment_core_pieces': 'http://localhost:8381/health',
            'segment_core_server': 'http://localhost:8389/health'
        }
    
    def check_api_health(self, timeout: int = 30) -> dict:
        """Check health of all APIs."""
        print("🔍 Checking API health...")
        
        health_status = {}
        
        for api_name, health_url in self.api_urls.items():
            try:
                response = requests.get(health_url, timeout=5)
                is_healthy = response.status_code == 200
                health_status[api_name] = is_healthy
                
                status_icon = "✅" if is_healthy else "❌"
                print(f"  {status_icon} {api_name}: {'Healthy' if is_healthy else 'Unhealthy'}")
                
            except Exception as e:
                health_status[api_name] = False
                print(f"  ❌ {api_name}: Not responding ({e})")
        
        healthy_count = sum(health_status.values())
        total_count = len(health_status)
        
        print(f"\n📊 API Health Summary: {healthy_count}/{total_count} APIs healthy")
        
        return health_status
    
    def check_external_services(self) -> dict:
        """Check if external service credentials are configured."""
        print("🔍 Checking external service configuration...")
        
        services = {
            'Roboflow': 'ROBOFLOW_API_KEY',
            'Google Vision': 'GOOGLE_VISION_API_KEY', 
            'Gemini AI': 'GEMINI_API_KEY',
            'Azure Storage': 'AZURE_STORAGE_CONNECTION_STRING'
        }
        
        service_status = {}
        
        for service_name, env_var in services.items():
            is_configured = bool(os.getenv(env_var))
            service_status[service_name] = is_configured
            
            status_icon = "✅" if is_configured else "⚠️"
            status_text = "Configured" if is_configured else "Not configured"
            print(f"  {status_icon} {service_name}: {status_text}")
        
        configured_count = sum(service_status.values())
        total_count = len(service_status)
        
        print(f"\n📊 External Services Summary: {configured_count}/{total_count} services configured")
        
        return service_status
    
    def run_tests(
        self,
        test_type: str = "all",
        include_external: bool = False,
        include_slow: bool = False,
        verbose: bool = False,
        output_file: str = None
    ) -> bool:
        """Run integration tests with specified options."""
        
        # Build pytest command
        cmd = ["python", "-m", "pytest", str(self.test_dir)]
        
        # Add markers based on test type
        markers = []
        
        if test_type == "api":
            markers.append("integration and not external and not e2e")
        elif test_type == "external":
            markers.append("external")
            include_external = True
        elif test_type == "e2e":
            markers.append("e2e")
        elif test_type == "all":
            markers.append("integration")
        
        # Exclude external services if not requested
        if not include_external:
            if markers:
                markers = [f"({m}) and not external" for m in markers]
            else:
                markers.append("not external")
        
        # Exclude slow tests if not requested
        if not include_slow:
            if markers:
                markers = [f"({m}) and not slow" for m in markers]
            else:
                markers.append("not slow")
        
        # Add marker filter to command
        if markers:
            cmd.extend(["-m", " and ".join(markers)])
        
        # Add verbosity
        if verbose:
            cmd.append("-v")
        else:
            cmd.append("-q")
        
        # Add output options
        cmd.extend([
            "--tb=short",
            "--strict-markers",
            "--strict-config"
        ])
        
        # Add coverage if requested
        cmd.extend([
            "--cov=services",
            "--cov=apis", 
            "--cov=processors",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/integration"
        ])
        
        # Add output file
        if output_file:
            cmd.extend(["--junitxml", output_file])
        
        print(f"🚀 Running integration tests...")
        print(f"📝 Command: {' '.join(cmd)}")
        print()
        
        # Run tests
        start_time = time.time()
        
        try:
            result = subprocess.run(cmd, cwd=Path(__file__).parent.parent, capture_output=False)
            success = result.returncode == 0
        except KeyboardInterrupt:
            print("\n🛑 Tests interrupted by user")
            return False
        except Exception as e:
            print(f"❌ Failed to run tests: {e}")
            return False
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  Tests completed in {duration:.2f} seconds")
        
        if success:
            print("✅ All integration tests passed!")
        else:
            print("❌ Some integration tests failed")
        
        return success
    
    def generate_report(self, health_status: dict, service_status: dict, test_results: bool) -> dict:
        """Generate a comprehensive test report."""
        report = {
            'timestamp': time.time(),
            'api_health': health_status,
            'external_services': service_status,
            'test_results': {
                'success': test_results,
                'apis_available': sum(health_status.values()),
                'total_apis': len(health_status),
                'services_configured': sum(service_status.values()),
                'total_services': len(service_status)
            }
        }
        
        return report
    
    def save_report(self, report: dict, output_file: str = "integration_test_report.json"):
        """Save test report to file."""
        report_path = Path(__file__).parent / output_file
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Test report saved to: {report_path}")


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Run aibase-ml integration tests')
    
    parser.add_argument(
        '--type', 
        choices=['all', 'api', 'external', 'e2e'], 
        default='all',
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--include-external', 
        action='store_true',
        help='Include tests that require external services'
    )
    
    parser.add_argument(
        '--include-slow', 
        action='store_true',
        help='Include slow-running tests'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    parser.add_argument(
        '--output',
        help='Output file for test results (JUnit XML format)'
    )
    
    parser.add_argument(
        '--report',
        help='Output file for test report (JSON format)'
    )
    
    parser.add_argument(
        '--skip-health-check',
        action='store_true',
        help='Skip API health checks'
    )
    
    parser.add_argument(
        '--fail-fast',
        action='store_true',
        help='Stop on first test failure'
    )
    
    args = parser.parse_args()
    
    runner = IntegrationTestRunner()
    
    print("🧪 aibase-ml Integration Test Runner")
    print("=" * 50)
    
    # Check API health
    if not args.skip_health_check:
        health_status = runner.check_api_health()
        
        # Check if any APIs are available
        if not any(health_status.values()):
            print("❌ No APIs are available. Please start the APIs before running integration tests.")
            sys.exit(1)
        
        # Warn if not all APIs are available
        if not all(health_status.values()):
            print("⚠️  Some APIs are not available. Some tests may be skipped.")
    else:
        health_status = {}
    
    # Check external services
    service_status = runner.check_external_services()
    
    if args.include_external and not any(service_status.values()):
        print("⚠️  External services requested but none are configured.")
    
    print()
    
    # Run tests
    test_success = runner.run_tests(
        test_type=args.type,
        include_external=args.include_external,
        include_slow=args.include_slow,
        verbose=args.verbose,
        output_file=args.output
    )
    
    # Generate and save report
    if args.report:
        report = runner.generate_report(health_status, service_status, test_success)
        runner.save_report(report, args.report)
    
    # Exit with appropriate code
    sys.exit(0 if test_success else 1)


if __name__ == "__main__":
    main()
