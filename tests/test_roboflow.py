import pytest
from utils.roboflow_async import call_roboflow_api_async, process_response
import aiohttp
from unittest.mock import patch

def test_process_response():
    """Test the process_response function with various input scenarios"""
    # Test with segment_flag True
    test_data = [{
        "segment_flag": True,
        "detection_flag": {"test": "data"},
        "google_vision_ocr": {"text": "test"},
        "google_gemini": {"output": "test"},
        "model_1_predictions": [{"class": "test"}]
    }]
    
    result = process_response(test_data)
    assert len(result) == 1
    assert "detection" in result[0]
    assert "google_vision_block_ocr" in result[0]
    assert "google_gemini_block_crop" in result[0]
    assert "segmentation" in result[0]
    assert isinstance(result[0]["segmentation"], list)

    # Test with single items in OCR and Gemini
    test_data = [{
        "segment_flag": True,
        "detection_flag": {"test": "data"},
        "google_vision_ocr": "single_ocr",
        "google_gemini": "single_gemini",
        "model_1_predictions": {"class": "test"}
    }]
    
    result = process_response(test_data)
    assert isinstance(result[0]["google_vision_block_ocr"], list)
    assert isinstance(result[0]["google_gemini_block_crop"], list)
    assert isinstance(result[0]["segmentation"], list)

@pytest.mark.asyncio
async def test_call_roboflow_api_async(mock_aiohttp_client, tmp_path):
    """Test the async Roboflow API call function"""
    # Create a test image file
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    result = await call_roboflow_api_async(
        image_path=str(test_file),
        depth_from=0.0,
        depth_to=10.0,
        segment_flag=False
    )
    
    assert result is not None
    assert "detection" in result
    assert "predictions" in result["detection"]

@pytest.mark.asyncio
async def test_roboflow_api_retry_mechanism(mock_aiohttp_client, tmp_path):
    """Test the retry mechanism of the Roboflow API call"""
    # Modify mock to fail first attempt
    mock_session = mock_aiohttp_client.return_value.__aenter__.return_value
    mock_post = mock_session.post.return_value.__aenter__
    
    # First call raises exception, second succeeds
    mock_post.side_effect = [
        aiohttp.ClientError("Test error"),
        type('AsyncResponse', (), {
            'status': 200,
            'json': lambda: pytest.async_return_value([{
                "detection": {"predictions": []},
                "segmentation": []
            }])
        })()
    ]
    
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    result = await call_roboflow_api_async(
        image_path=str(test_file),
        depth_from=0.0,
        depth_to=10.0,
        segment_flag=False,
        max_retries=2
    )
    
    assert result is not None
    assert "detection" in result

@pytest.mark.asyncio
async def test_roboflow_api_with_custom_prompt(mock_aiohttp_client, tmp_path):
    """Test API call with custom prompt"""
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    custom_prompt = "Custom test prompt"
    result = await call_roboflow_api_async(
        image_path=str(test_file),
        depth_from=0.0,
        depth_to=10.0,
        user_prompt=custom_prompt,
        segment_flag=False
    )
    
    # Verify the custom prompt was used in the API call
    mock_session = mock_aiohttp_client.return_value.__aenter__.return_value
    called_args = mock_session.post.call_args
    assert called_args is not None
    assert "json" in called_args.kwargs
    assert called_args.kwargs["json"]["parameters"]["block_prompt"] == custom_prompt

@pytest.mark.asyncio
async def test_roboflow_api_with_segment_flag(mock_aiohttp_client, tmp_path):
    """Test API call with segment_flag enabled"""
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    result = await call_roboflow_api_async(
        image_path=str(test_file),
        depth_from=0.0,
        depth_to=10.0,
        segment_flag=True
    )
    
    # Verify segment_flag was properly handled
    mock_session = mock_aiohttp_client.return_value.__aenter__.return_value
    called_args = mock_session.post.call_args
    assert called_args is not None
    assert "json" in called_args.kwargs
    assert called_args.kwargs["json"]["parameters"]["segment_flag"] is True

@pytest.mark.asyncio
async def test_roboflow_api_error_handling(mock_aiohttp_client, tmp_path):
    """Test error handling in API call"""
    # Modify mock to always fail
    mock_session = mock_aiohttp_client.return_value.__aenter__.return_value
    mock_session.post.return_value.__aenter__.side_effect = aiohttp.ClientError("Test error")
    
    test_file = tmp_path / "test.jpg"
    test_file.write_bytes(b"fake image data")
    
    result = await call_roboflow_api_async(
        image_path=str(test_file),
        depth_from=0.0,
        depth_to=10.0,
        segment_flag=False,
        max_retries=1
    )
    
    assert "error" in result
    assert "Failed to fetch results" in result["error"]

@pytest.mark.integration
@pytest.mark.asyncio
async def test_roboflow_integration():
    """
    Integration test for Roboflow API
    Requires ROBOFLOW_API_KEY environment variable
    """
    import os
    if "ROBOFLOW_API_KEY" not in os.environ:
        pytest.skip("ROBOFLOW_API_KEY not set")
        
    import numpy as np
    from PIL import Image
    
    # Create a test image
    test_file = "test_integration.jpg"
    img_array = np.zeros((100, 100, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    img.save(test_file)
    
    try:
        result = await call_roboflow_api_async(
            image_path=test_file,
            depth_from=0.0,
            depth_to=10.0,
            segment_flag=False
        )
        
        assert result is not None
        assert isinstance(result, dict)
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)