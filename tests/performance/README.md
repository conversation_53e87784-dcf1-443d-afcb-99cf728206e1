# Performance Testing for aibase-ml

This directory contains comprehensive performance testing tools for the aibase-ml project. The performance testing suite includes benchmarking, load testing, stress testing, and continuous monitoring capabilities.

## Overview

The performance testing framework provides:

- **Benchmarking**: Measure baseline API performance
- **Load Testing**: Test API behavior under various load conditions
- **Stress Testing**: Find breaking points and maximum capacity
- **Spike Testing**: Test response to sudden load increases
- **Continuous Monitoring**: Real-time performance tracking with alerting
- **Comprehensive Reporting**: Detailed analysis and visualizations

## Components

### 1. Benchmark Runner (`benchmark_runner.py`)
Measures baseline API performance with detailed metrics:
- Response time statistics (avg, min, max, percentiles)
- Memory and CPU usage monitoring
- Success rate tracking
- Concurrent request testing
- Performance visualization charts

### 2. Performance Monitor (`performance_monitor.py`)
Real-time monitoring with alerting:
- Continuous performance tracking
- Configurable threshold-based alerting
- Historical data collection
- Performance dashboard generation
- System resource monitoring

### 3. Load Tester (`load_tester.py`)
Comprehensive load testing capabilities:
- Concurrent user simulation
- Gradual load ramping
- Stress testing to failure points
- Spike testing scenarios
- Breaking point detection

### 4. Performance Test Runner (`run_performance_tests.py`)
Unified test execution and reporting:
- Orchestrates all performance tests
- Generates comprehensive reports
- Configurable test scenarios
- Automated result analysis

## Quick Start

### Basic Performance Test
```bash
# Run all performance tests
python tests/performance/run_performance_tests.py

# Run only benchmarks
python tests/performance/run_performance_tests.py --test-type benchmark

# Run with specific APIs
python tests/performance/run_performance_tests.py --apis main_processing segment_auto_crop
```

### Individual Component Usage
```python
from tests.performance import BenchmarkRunner, PerformanceMonitor, LoadTester

# Benchmark testing
runner = BenchmarkRunner()
results = runner.run_single_api_benchmark('main_processing', num_requests=10)
runner.print_summary('main_processing')

# Load testing
load_tester = LoadTester()
config = LoadTestConfig(
    api_name='main_processing',
    concurrent_users=5,
    requests_per_user=10,
    test_duration=60
)
result = load_tester.run_load_test(config)

# Performance monitoring
monitor = PerformanceMonitor()
monitor.start_monitoring(interval=5.0)
# ... run your application ...
monitor.stop_monitoring()
```

## Test Types

### 1. Benchmark Tests
Measure baseline performance with single and concurrent requests:

```bash
# Basic benchmark
python tests/performance/run_performance_tests.py --test-type benchmark --requests 20

# Include concurrent testing
python tests/performance/run_performance_tests.py --test-type benchmark --concurrent
```

**Metrics Collected:**
- Average, minimum, maximum response times
- 50th, 95th, 99th percentile response times
- Success rate percentage
- Memory usage (average and peak)
- CPU usage (average and peak)
- Requests per second throughput

### 2. Load Tests
Test API behavior under sustained load:

```bash
# Standard load test scenarios
python tests/performance/run_performance_tests.py --test-type load

# Custom load test
python -c "
from tests.performance import LoadTester, LoadTestConfig
tester = LoadTester()
config = LoadTestConfig('main_processing', 10, 5, 30.0, 120.0, 2.0)
result = tester.run_load_test(config)
tester.print_load_test_summary(result)
"
```

**Default Scenarios:**
- **Light Load**: 3 users, 5 requests each, 60 seconds
- **Medium Load**: 8 users, 8 requests each, 120 seconds  
- **Heavy Load**: 15 users, 10 requests each, 180 seconds

### 3. Stress Tests
Find breaking points and maximum capacity:

```bash
# Stress test with default settings
python tests/performance/run_performance_tests.py --test-type stress

# Custom maximum users
python tests/performance/run_performance_tests.py --test-type stress --max-users 50
```

**Process:**
- Gradually increases concurrent users (steps of 5)
- Tests until success rate drops below 80% or response time exceeds 60s
- Identifies breaking point for each API

### 4. Spike Tests
Test response to sudden load increases:

```bash
# Spike testing
python tests/performance/run_performance_tests.py --test-type spike
```

**Scenarios:**
- **Baseline**: 3 concurrent users for 60 seconds
- **Spike**: Sudden increase to 20 concurrent users

### 5. Monitoring Tests
Continuous performance monitoring with alerting:

```bash
# Monitor for 30 minutes
python tests/performance/run_performance_tests.py --test-type monitoring --monitoring-duration 30
```

**Features:**
- Real-time metric collection
- Threshold-based alerting
- Background load simulation
- Performance dashboard generation

## Configuration

### Performance Thresholds
Default thresholds for alerting:

```python
thresholds = {
    'response_time': {
        'warning': 30.0,    # seconds
        'critical': 60.0    # seconds
    },
    'memory_usage': {
        'warning': 1000.0,  # MB
        'critical': 2000.0  # MB
    },
    'cpu_usage': {
        'warning': 80.0,    # percent
        'critical': 95.0    # percent
    },
    'success_rate': {
        'warning': 0.9,     # 90%
        'critical': 0.8     # 80%
    }
}
```

### API Configuration
APIs tested by default:

```python
api_configs = {
    'main_processing': 'http://localhost:8386',
    'segment_auto_crop': 'http://localhost:8388',
    'segment_core_pieces': 'http://localhost:8381',
    'segment_core_server': 'http://localhost:8389'
}
```

## Prerequisites

### 1. Running APIs
All APIs must be running before performance testing:

```bash
# Terminal 1
cd apis && python main_processing.py

# Terminal 2  
cd apis && python segment_auto_crop.py

# Terminal 3
cd apis && python segment_core_pieces.py

# Terminal 4
cd apis && python segment_core_server.py
```

### 2. Dependencies
Install performance testing dependencies:

```bash
pip install psutil matplotlib numpy requests
```

### 3. System Resources
Ensure adequate system resources:
- **Memory**: At least 4GB available
- **CPU**: Multi-core recommended for concurrent testing
- **Disk**: Space for test images and reports

## Output and Reports

### Report Structure
```
performance_reports/
├── performance_test_results.json    # Raw test data
├── performance_summary.txt          # Text summary
├── benchmark_charts/                # Benchmark visualizations
│   ├── response_times.png
│   ├── success_rates.png
│   └── resource_usage.png
├── monitoring_dashboard.png         # Real-time monitoring
├── main_processing_load_test.png    # Load test charts
├── segment_auto_crop_load_test.png
├── segment_core_pieces_load_test.png
└── segment_core_server_load_test.png
```

### Key Metrics in Reports

**Response Time Analysis:**
- Average response time trends
- Percentile distributions (P50, P95, P99)
- Response time vs. concurrent users

**Throughput Analysis:**
- Requests per second capacity
- Throughput vs. load relationship
- Breaking point identification

**Resource Usage:**
- Memory consumption patterns
- CPU utilization trends
- Resource efficiency analysis

**Reliability Metrics:**
- Success rate under various loads
- Error rate analysis
- Failure pattern identification

## Performance Benchmarks

### Expected Performance Targets

**Response Times:**
- **Segment APIs**: < 30 seconds average
- **Processing APIs**: < 60 seconds average
- **95th Percentile**: < 2x average response time

**Throughput:**
- **Minimum**: 0.5 requests/second per API
- **Target**: 1.0 requests/second per API
- **Concurrent**: 80%+ success rate with 3 concurrent users

**Resource Usage:**
- **Memory**: < 1GB per API instance
- **CPU**: < 80% average utilization
- **Success Rate**: > 95% under normal load

### Performance Optimization Guidelines

**API Response Time:**
1. Monitor database query performance
2. Optimize image processing pipelines
3. Implement response caching where appropriate
4. Use asynchronous processing for long operations

**Memory Usage:**
1. Implement proper image cleanup
2. Monitor for memory leaks
3. Use streaming for large file processing
4. Optimize model loading and caching

**Concurrent Performance:**
1. Implement proper connection pooling
2. Use thread-safe operations
3. Monitor resource contention
4. Implement rate limiting if needed

## Troubleshooting

### Common Issues

#### High Response Times
```
Average response time: 45.2s (Warning threshold: 30.0s)
```
**Solutions:**
- Check external service latency (Roboflow, Google Vision, etc.)
- Monitor system resource usage
- Verify network connectivity
- Review image processing efficiency

#### Low Success Rates
```
Success rate: 75% (Critical threshold: 80%)
```
**Solutions:**
- Check API error logs
- Verify external service availability
- Monitor timeout configurations
- Review concurrent request limits

#### Memory Issues
```
Memory usage: 1.2GB (Warning threshold: 1.0GB)
```
**Solutions:**
- Check for memory leaks
- Optimize image processing
- Implement garbage collection
- Monitor long-running processes

#### Performance Degradation
```
Throughput decreased from 1.2 to 0.8 requests/second
```
**Solutions:**
- Compare with baseline benchmarks
- Check system resource availability
- Monitor external service performance
- Review recent code changes

### Debug Mode
Enable detailed logging for troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run performance tests with debug output
runner = BenchmarkRunner()
results = runner.run_single_api_benchmark('main_processing', num_requests=5)
```

### Health Checks
Verify API availability before testing:

```python
from tests.performance import BenchmarkRunner

runner = BenchmarkRunner()
for api_name, config in runner.api_configs.items():
    try:
        response = requests.get(f"{config['base_url']}/health", timeout=5)
        print(f"{api_name}: {'✅' if response.status_code == 200 else '❌'}")
    except:
        print(f"{api_name}: ❌ Not responding")
```

## Continuous Integration

### GitHub Actions Integration
```yaml
name: Performance Tests
on: [push, pull_request]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: pip install -r requirements.txt
      
      - name: Start APIs
        run: |
          python apis/main_processing.py &
          python apis/segment_auto_crop.py &
          python apis/segment_core_pieces.py &
          python apis/segment_core_server.py &
          sleep 30  # Wait for APIs to start
      
      - name: Run Performance Tests
        run: |
          python tests/performance/run_performance_tests.py \
            --test-type benchmark \
            --requests 5 \
            --output-dir performance_results
      
      - name: Upload Results
        uses: actions/upload-artifact@v2
        with:
          name: performance-results
          path: performance_results/
```

### Performance Regression Detection
Set up automated performance regression detection:

```python
# Compare current results with baseline
def check_performance_regression(current_results, baseline_file):
    with open(baseline_file) as f:
        baseline = json.load(f)
    
    for api_name, current in current_results.items():
        baseline_time = baseline[api_name]['avg_response_time']
        current_time = current['avg_response_time']
        
        regression = (current_time - baseline_time) / baseline_time
        if regression > 0.2:  # 20% regression threshold
            print(f"⚠️ Performance regression detected in {api_name}: {regression:.1%}")
```

## Contributing

When adding new performance tests:

1. **Follow naming conventions**: Use descriptive test names
2. **Include proper cleanup**: Ensure resources are released
3. **Document thresholds**: Clearly define performance expectations
4. **Add visualizations**: Include charts for complex metrics
5. **Update documentation**: Keep README current with new features

## Support

For performance testing issues:
1. Check API health and availability
2. Verify system resource availability
3. Review performance test logs
4. Compare with baseline benchmarks
5. Check external service status
