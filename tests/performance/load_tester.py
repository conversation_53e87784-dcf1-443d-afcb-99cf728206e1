"""
Load testing and stress testing for aibase-ml APIs.

Provides comprehensive load testing capabilities including:
- Concurrent user simulation
- Gradual load ramping
- Stress testing to failure points
- Resource exhaustion testing
"""

import time
import threading
import queue
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
import numpy as np
import matplotlib.pyplot as plt
from .benchmark_runner import BenchmarkResult, BenchmarkRunner


@dataclass
class LoadTestConfig:
    """Configuration for load testing."""
    api_name: str
    concurrent_users: int
    requests_per_user: int
    ramp_up_time: float  # seconds to reach full load
    test_duration: float  # total test duration in seconds
    think_time: float  # delay between requests per user
    image_size: Tuple[int, int] = (800, 600)


@dataclass
class LoadTestResult:
    """Results from a load test."""
    config: LoadTestConfig
    start_time: float
    end_time: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    errors: List[str]
    detailed_results: List[BenchmarkResult]


class LoadTester:
    """Load testing framework for aibase-ml APIs."""
    
    def __init__(self):
        self.benchmark_runner = BenchmarkRunner()
        self.results_queue = queue.Queue()
        self.stop_event = threading.Event()
    
    def run_load_test(self, config: LoadTestConfig) -> LoadTestResult:
        """Run a comprehensive load test."""
        print(f"🚀 Starting load test for {config.api_name}")
        print(f"   Users: {config.concurrent_users}")
        print(f"   Requests per user: {config.requests_per_user}")
        print(f"   Ramp-up time: {config.ramp_up_time}s")
        print(f"   Test duration: {config.test_duration}s")
        
        # Clear previous results
        self.results_queue = queue.Queue()
        self.stop_event.clear()
        
        start_time = time.time()
        
        # Create test image data
        image_data = self.benchmark_runner.create_test_image_data(config.image_size)
        
        # Start user threads with ramp-up
        user_threads = []
        ramp_delay = config.ramp_up_time / config.concurrent_users if config.concurrent_users > 0 else 0
        
        for user_id in range(config.concurrent_users):
            thread = threading.Thread(
                target=self._user_simulation,
                args=(user_id, config, image_data, user_id * ramp_delay)
            )
            user_threads.append(thread)
            thread.start()
        
        # Monitor test duration
        monitor_thread = threading.Thread(
            target=self._test_duration_monitor,
            args=(config.test_duration,)
        )
        monitor_thread.start()
        
        # Wait for all threads to complete
        for thread in user_threads:
            thread.join()
        
        monitor_thread.join()
        
        end_time = time.time()
        
        # Collect results
        detailed_results = []
        while not self.results_queue.empty():
            try:
                result = self.results_queue.get_nowait()
                detailed_results.append(result)
            except queue.Empty:
                break
        
        # Calculate summary statistics
        return self._calculate_load_test_summary(config, start_time, end_time, detailed_results)
    
    def _user_simulation(self, user_id: int, config: LoadTestConfig, image_data: bytes, start_delay: float):
        """Simulate a single user's behavior."""
        # Wait for ramp-up delay
        time.sleep(start_delay)
        
        requests_made = 0
        
        while not self.stop_event.is_set() and requests_made < config.requests_per_user:
            try:
                # Make API request
                result = self.benchmark_runner.make_api_request(config.api_name, image_data)
                result.timestamp = time.time()  # Update timestamp for load test
                
                # Add user context
                result.api_name = f"{config.api_name}_user_{user_id}"
                
                self.results_queue.put(result)
                requests_made += 1
                
                # Think time between requests
                if config.think_time > 0 and not self.stop_event.is_set():
                    time.sleep(config.think_time)
                    
            except Exception as e:
                # Create error result
                error_result = BenchmarkResult(
                    api_name=f"{config.api_name}_user_{user_id}",
                    endpoint=self.benchmark_runner.api_configs[config.api_name]['endpoint'],
                    response_time=0.0,
                    status_code=0,
                    success=False,
                    memory_usage_mb=0.0,
                    cpu_usage_percent=0.0,
                    response_size_bytes=0,
                    timestamp=time.time(),
                    error_message=str(e)[:200]
                )
                self.results_queue.put(error_result)
                requests_made += 1
    
    def _test_duration_monitor(self, duration: float):
        """Monitor test duration and stop when time limit reached."""
        time.sleep(duration)
        self.stop_event.set()
    
    def _calculate_load_test_summary(
        self,
        config: LoadTestConfig,
        start_time: float,
        end_time: float,
        detailed_results: List[BenchmarkResult]
    ) -> LoadTestResult:
        """Calculate summary statistics from load test results."""
        
        if not detailed_results:
            return LoadTestResult(
                config=config,
                start_time=start_time,
                end_time=end_time,
                total_requests=0,
                successful_requests=0,
                failed_requests=0,
                success_rate=0.0,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                p50_response_time=0.0,
                p95_response_time=0.0,
                p99_response_time=0.0,
                requests_per_second=0.0,
                errors=[],
                detailed_results=[]
            )
        
        successful_results = [r for r in detailed_results if r.success]
        failed_results = [r for r in detailed_results if not r.success]
        
        # Calculate response time statistics
        if successful_results:
            response_times = [r.response_time for r in successful_results]
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p50_response_time = statistics.median(response_times)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
        else:
            avg_response_time = 0.0
            min_response_time = 0.0
            max_response_time = 0.0
            p50_response_time = 0.0
            p95_response_time = 0.0
            p99_response_time = 0.0
        
        # Calculate throughput
        total_duration = end_time - start_time
        requests_per_second = len(successful_results) / total_duration if total_duration > 0 else 0
        
        # Collect error messages
        errors = [r.error_message for r in failed_results if r.error_message]
        unique_errors = list(set(errors))
        
        return LoadTestResult(
            config=config,
            start_time=start_time,
            end_time=end_time,
            total_requests=len(detailed_results),
            successful_requests=len(successful_results),
            failed_requests=len(failed_results),
            success_rate=len(successful_results) / len(detailed_results),
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            errors=unique_errors,
            detailed_results=detailed_results
        )
    
    def run_stress_test(self, api_name: str, max_users: int = 50, step_size: int = 5) -> List[LoadTestResult]:
        """Run stress test by gradually increasing load until failure."""
        print(f"💪 Starting stress test for {api_name}")
        print(f"   Max users: {max_users}")
        print(f"   Step size: {step_size}")
        
        stress_results = []
        current_users = step_size
        
        while current_users <= max_users:
            print(f"\n🔥 Testing with {current_users} concurrent users...")
            
            config = LoadTestConfig(
                api_name=api_name,
                concurrent_users=current_users,
                requests_per_user=5,  # Fewer requests per user for stress testing
                ramp_up_time=10.0,    # Quick ramp-up
                test_duration=60.0,   # 1 minute test
                think_time=1.0        # 1 second between requests
            )
            
            result = self.run_load_test(config)
            stress_results.append(result)
            
            print(f"   Success rate: {result.success_rate:.1%}")
            print(f"   Avg response time: {result.avg_response_time:.2f}s")
            print(f"   Requests/second: {result.requests_per_second:.2f}")
            
            # Check if we've reached failure point
            if result.success_rate < 0.8:  # Less than 80% success rate
                print(f"🚨 Failure threshold reached at {current_users} users")
                break
            
            if result.avg_response_time > 60.0:  # Response time too high
                print(f"🚨 Response time threshold exceeded at {current_users} users")
                break
            
            current_users += step_size
            
            # Brief pause between stress levels
            time.sleep(5)
        
        return stress_results
    
    def run_spike_test(self, api_name: str, baseline_users: int = 5, spike_users: int = 25) -> Dict[str, LoadTestResult]:
        """Run spike test with sudden load increase."""
        print(f"⚡ Starting spike test for {api_name}")
        print(f"   Baseline users: {baseline_users}")
        print(f"   Spike users: {spike_users}")
        
        results = {}
        
        # Baseline test
        print("\n📊 Running baseline test...")
        baseline_config = LoadTestConfig(
            api_name=api_name,
            concurrent_users=baseline_users,
            requests_per_user=10,
            ramp_up_time=5.0,
            test_duration=60.0,
            think_time=2.0
        )
        
        results['baseline'] = self.run_load_test(baseline_config)
        
        # Brief pause
        time.sleep(10)
        
        # Spike test
        print("\n⚡ Running spike test...")
        spike_config = LoadTestConfig(
            api_name=api_name,
            concurrent_users=spike_users,
            requests_per_user=10,
            ramp_up_time=1.0,  # Very quick ramp-up for spike
            test_duration=60.0,
            think_time=1.0
        )
        
        results['spike'] = self.run_load_test(spike_config)
        
        return results
    
    def create_load_test_report(self, results: List[LoadTestResult], output_file: str = "load_test_report.png"):
        """Create comprehensive load test visualization."""
        if not results:
            print("No results to visualize")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Extract data for plotting
        user_counts = [r.config.concurrent_users for r in results]
        success_rates = [r.success_rate * 100 for r in results]
        avg_response_times = [r.avg_response_time for r in results]
        requests_per_second = [r.requests_per_second for r in results]
        p95_response_times = [r.p95_response_time for r in results]
        
        # Success rate vs concurrent users
        ax1.plot(user_counts, success_rates, 'bo-', linewidth=2, markersize=6)
        ax1.set_title('Success Rate vs Concurrent Users', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Concurrent Users')
        ax1.set_ylabel('Success Rate (%)')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=95, color='red', linestyle='--', alpha=0.7, label='Target (95%)')
        ax1.legend()
        
        # Response time vs concurrent users
        ax2.plot(user_counts, avg_response_times, 'go-', linewidth=2, markersize=6, label='Average')
        ax2.plot(user_counts, p95_response_times, 'ro-', linewidth=2, markersize=6, label='95th Percentile')
        ax2.set_title('Response Time vs Concurrent Users', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Concurrent Users')
        ax2.set_ylabel('Response Time (seconds)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Throughput vs concurrent users
        ax3.plot(user_counts, requests_per_second, 'mo-', linewidth=2, markersize=6)
        ax3.set_title('Throughput vs Concurrent Users', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Concurrent Users')
        ax3.set_ylabel('Requests per Second')
        ax3.grid(True, alpha=0.3)
        
        # Error rate vs concurrent users
        error_rates = [(1 - r.success_rate) * 100 for r in results]
        ax4.plot(user_counts, error_rates, 'ro-', linewidth=2, markersize=6)
        ax4.set_title('Error Rate vs Concurrent Users', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Concurrent Users')
        ax4.set_ylabel('Error Rate (%)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Load test report saved to: {output_file}")
    
    def save_load_test_results(self, results: List[LoadTestResult], output_file: str = "load_test_results.json"):
        """Save load test results to file."""
        output_path = Path(output_file)
        
        # Convert results to dictionaries
        results_data = []
        for result in results:
            result_dict = asdict(result)
            # Convert detailed results to dictionaries
            result_dict['detailed_results'] = [asdict(dr) for dr in result.detailed_results]
            results_data.append(result_dict)
        
        output_data = {
            'timestamp': time.time(),
            'total_tests': len(results),
            'results': results_data
        }
        
        with open(output_path, 'w') as f:
            import json
            json.dump(output_data, f, indent=2, default=str)
        
        print(f"💾 Load test results saved to: {output_path}")
        return output_path
    
    def print_load_test_summary(self, result: LoadTestResult):
        """Print summary of a single load test."""
        print(f"\n📊 LOAD TEST SUMMARY - {result.config.api_name}")
        print("=" * 60)
        print(f"Configuration:")
        print(f"  Concurrent Users:     {result.config.concurrent_users}")
        print(f"  Requests per User:    {result.config.requests_per_user}")
        print(f"  Test Duration:        {result.config.test_duration}s")
        print(f"  Ramp-up Time:         {result.config.ramp_up_time}s")
        print()
        print(f"Results:")
        print(f"  Total Requests:       {result.total_requests}")
        print(f"  Successful:           {result.successful_requests}")
        print(f"  Failed:               {result.failed_requests}")
        print(f"  Success Rate:         {result.success_rate:.1%}")
        print(f"  Requests/Second:      {result.requests_per_second:.2f}")
        print()
        print(f"Response Times:")
        print(f"  Average:              {result.avg_response_time:.2f}s")
        print(f"  Minimum:              {result.min_response_time:.2f}s")
        print(f"  Maximum:              {result.max_response_time:.2f}s")
        print(f"  50th Percentile:      {result.p50_response_time:.2f}s")
        print(f"  95th Percentile:      {result.p95_response_time:.2f}s")
        print(f"  99th Percentile:      {result.p99_response_time:.2f}s")
        
        if result.errors:
            print(f"\nErrors ({len(result.errors)} unique):")
            for i, error in enumerate(result.errors[:5]):  # Show first 5 errors
                print(f"  {i+1}. {error}")
            if len(result.errors) > 5:
                print(f"  ... and {len(result.errors) - 5} more")
    
    def find_breaking_point(self, api_name: str) -> Optional[int]:
        """Find the breaking point (maximum concurrent users) for an API."""
        print(f"🔍 Finding breaking point for {api_name}...")
        
        # Binary search for breaking point
        low = 1
        high = 100
        last_successful = 0
        
        while low <= high:
            mid = (low + high) // 2
            
            print(f"   Testing {mid} concurrent users...")
            
            config = LoadTestConfig(
                api_name=api_name,
                concurrent_users=mid,
                requests_per_user=3,
                ramp_up_time=5.0,
                test_duration=30.0,
                think_time=1.0
            )
            
            result = self.run_load_test(config)
            
            # Consider successful if success rate > 90% and avg response time < 30s
            if result.success_rate > 0.9 and result.avg_response_time < 30.0:
                last_successful = mid
                low = mid + 1
                print(f"   ✅ {mid} users: Success rate {result.success_rate:.1%}, Avg time {result.avg_response_time:.2f}s")
            else:
                high = mid - 1
                print(f"   ❌ {mid} users: Success rate {result.success_rate:.1%}, Avg time {result.avg_response_time:.2f}s")
            
            # Brief pause between tests
            time.sleep(5)
        
        print(f"🎯 Breaking point for {api_name}: {last_successful} concurrent users")
        return last_successful if last_successful > 0 else None
