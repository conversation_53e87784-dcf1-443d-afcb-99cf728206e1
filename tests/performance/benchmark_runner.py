"""
Performance benchmark runner for aibase-ml APIs.

Provides comprehensive performance testing including response times,
memory usage, and throughput measurements.
"""

import time
import psutil
import requests
import statistics
import json
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import matplotlib.pyplot as plt
import numpy as np


@dataclass
class BenchmarkResult:
    """Results from a single benchmark run."""
    api_name: str
    endpoint: str
    response_time: float
    status_code: int
    success: bool
    memory_usage_mb: float
    cpu_usage_percent: float
    response_size_bytes: int
    timestamp: float
    error_message: Optional[str] = None


@dataclass
class BenchmarkSummary:
    """Summary statistics for benchmark results."""
    api_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    avg_memory_usage_mb: float
    max_memory_usage_mb: float
    avg_cpu_usage_percent: float
    max_cpu_usage_percent: float
    total_duration: float
    requests_per_second: float


class BenchmarkRunner:
    """Performance benchmark runner for aibase-ml APIs."""
    
    def __init__(self):
        self.api_configs = {
            'main_processing': {
                'base_url': 'http://localhost:8386',
                'endpoint': '/process',
                'method': 'POST',
                'timeout': 120
            },
            'segment_auto_crop': {
                'base_url': 'http://localhost:8388',
                'endpoint': '/segment_crop',
                'method': 'POST',
                'timeout': 60
            },
            'segment_core_pieces': {
                'base_url': 'http://localhost:8381',
                'endpoint': '/segment_core_pieces',
                'method': 'POST',
                'timeout': 60
            },
            'segment_core_server': {
                'base_url': 'http://localhost:8389',
                'endpoint': '/process_core_outline',
                'method': 'POST',
                'timeout': 120
            }
        }
        
        self.results: List[BenchmarkResult] = []
        self.process = psutil.Process()
    
    def create_test_image_data(self, size: Tuple[int, int] = (800, 600)) -> bytes:
        """Create test image data for benchmarking."""
        import cv2
        import numpy as np
        
        height, width = size
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add simple pattern
        image.fill(200)
        cv2.rectangle(image, (50, 50), (width-50, height-50), (0, 0, 255), 5)
        
        # Add some segments
        for i in range(5):
            y_start = 70 + i * 100
            y_end = y_start + 80
            if y_end < height - 50:
                cv2.rectangle(image, (70, y_start), (width-70, y_end), (100, 150, 100), -1)
        
        # Encode as JPEG
        _, encoded = cv2.imencode('.jpg', image)
        return encoded.tobytes()
    
    def measure_system_resources(self) -> Tuple[float, float]:
        """Measure current memory and CPU usage."""
        try:
            memory_mb = self.process.memory_info().rss / 1024 / 1024
            cpu_percent = self.process.cpu_percent()
            return memory_mb, cpu_percent
        except:
            return 0.0, 0.0
    
    def make_api_request(
        self,
        api_name: str,
        image_data: bytes,
        custom_params: Optional[Dict[str, Any]] = None
    ) -> BenchmarkResult:
        """Make a single API request and measure performance."""
        config = self.api_configs[api_name]
        url = f"{config['base_url']}{config['endpoint']}"
        
        # Prepare request data
        files = {'file': ('test.jpg', image_data, 'image/jpeg')}
        data = custom_params or {}
        
        # Add default parameters based on API
        if api_name == 'main_processing':
            data.setdefault('use_segmentation', 'false')  # Faster for benchmarking
        elif api_name == 'segment_core_server':
            data.setdefault('use_row', 'false')  # Faster for benchmarking
        
        # Measure resources before request
        memory_before, cpu_before = self.measure_system_resources()
        
        start_time = time.time()
        
        try:
            response = requests.post(
                url,
                files=files,
                data=data,
                timeout=config['timeout']
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Measure resources after request
            memory_after, cpu_after = self.measure_system_resources()
            
            success = response.status_code == 200
            response_size = len(response.content) if response.content else 0
            
            return BenchmarkResult(
                api_name=api_name,
                endpoint=config['endpoint'],
                response_time=response_time,
                status_code=response.status_code,
                success=success,
                memory_usage_mb=max(memory_after, memory_before),
                cpu_usage_percent=max(cpu_after, cpu_before),
                response_size_bytes=response_size,
                timestamp=start_time,
                error_message=None if success else response.text[:200]
            )
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            memory_after, cpu_after = self.measure_system_resources()
            
            return BenchmarkResult(
                api_name=api_name,
                endpoint=config['endpoint'],
                response_time=response_time,
                status_code=0,
                success=False,
                memory_usage_mb=max(memory_after, memory_before),
                cpu_usage_percent=max(cpu_after, cpu_before),
                response_size_bytes=0,
                timestamp=start_time,
                error_message=str(e)[:200]
            )
    
    def run_single_api_benchmark(
        self,
        api_name: str,
        num_requests: int = 10,
        image_size: Tuple[int, int] = (800, 600),
        custom_params: Optional[Dict[str, Any]] = None
    ) -> List[BenchmarkResult]:
        """Run benchmark for a single API."""
        print(f"🔥 Benchmarking {api_name} with {num_requests} requests...")
        
        # Create test image data
        image_data = self.create_test_image_data(image_size)
        
        results = []
        
        for i in range(num_requests):
            print(f"  Request {i+1}/{num_requests}...", end=" ")
            
            result = self.make_api_request(api_name, image_data, custom_params)
            results.append(result)
            
            status_icon = "✅" if result.success else "❌"
            print(f"{status_icon} {result.response_time:.2f}s")
            
            # Small delay between requests to avoid overwhelming the API
            time.sleep(0.5)
        
        self.results.extend(results)
        return results
    
    def run_concurrent_benchmark(
        self,
        api_name: str,
        num_requests: int = 10,
        max_workers: int = 3,
        image_size: Tuple[int, int] = (800, 600)
    ) -> List[BenchmarkResult]:
        """Run concurrent benchmark for a single API."""
        print(f"🚀 Concurrent benchmarking {api_name} with {num_requests} requests, {max_workers} workers...")
        
        # Create test image data
        image_data = self.create_test_image_data(image_size)
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all requests
            futures = [
                executor.submit(self.make_api_request, api_name, image_data)
                for _ in range(num_requests)
            ]
            
            # Collect results as they complete
            for i, future in enumerate(as_completed(futures)):
                result = future.result()
                results.append(result)
                
                status_icon = "✅" if result.success else "❌"
                print(f"  Request {i+1}/{num_requests} completed: {status_icon} {result.response_time:.2f}s")
        
        self.results.extend(results)
        return results
    
    def run_all_apis_benchmark(
        self,
        num_requests_per_api: int = 5,
        image_size: Tuple[int, int] = (800, 600)
    ) -> Dict[str, List[BenchmarkResult]]:
        """Run benchmark for all APIs."""
        print("🎯 Running benchmark for all APIs...")
        
        all_results = {}
        
        for api_name in self.api_configs.keys():
            try:
                # Check if API is available
                health_url = f"{self.api_configs[api_name]['base_url']}/health"
                response = requests.get(health_url, timeout=5)
                
                if response.status_code != 200:
                    print(f"⚠️  {api_name} not available, skipping...")
                    continue
                
                results = self.run_single_api_benchmark(
                    api_name, num_requests_per_api, image_size
                )
                all_results[api_name] = results
                
            except Exception as e:
                print(f"❌ Failed to benchmark {api_name}: {e}")
                continue
        
        return all_results
    
    def calculate_summary(self, results: List[BenchmarkResult]) -> BenchmarkSummary:
        """Calculate summary statistics from benchmark results."""
        if not results:
            raise ValueError("No results to summarize")
        
        api_name = results[0].api_name
        successful_results = [r for r in results if r.success]
        
        if not successful_results:
            # All requests failed
            return BenchmarkSummary(
                api_name=api_name,
                total_requests=len(results),
                successful_requests=0,
                failed_requests=len(results),
                success_rate=0.0,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                p50_response_time=0.0,
                p95_response_time=0.0,
                p99_response_time=0.0,
                avg_memory_usage_mb=0.0,
                max_memory_usage_mb=0.0,
                avg_cpu_usage_percent=0.0,
                max_cpu_usage_percent=0.0,
                total_duration=0.0,
                requests_per_second=0.0
            )
        
        # Calculate statistics from successful results
        response_times = [r.response_time for r in successful_results]
        memory_usage = [r.memory_usage_mb for r in successful_results]
        cpu_usage = [r.cpu_usage_percent for r in successful_results]
        
        # Calculate duration
        timestamps = [r.timestamp for r in results]
        total_duration = max(timestamps) - min(timestamps) + max(response_times)
        
        return BenchmarkSummary(
            api_name=api_name,
            total_requests=len(results),
            successful_requests=len(successful_results),
            failed_requests=len(results) - len(successful_results),
            success_rate=len(successful_results) / len(results),
            avg_response_time=statistics.mean(response_times),
            min_response_time=min(response_times),
            max_response_time=max(response_times),
            p50_response_time=statistics.median(response_times),
            p95_response_time=np.percentile(response_times, 95),
            p99_response_time=np.percentile(response_times, 99),
            avg_memory_usage_mb=statistics.mean(memory_usage),
            max_memory_usage_mb=max(memory_usage),
            avg_cpu_usage_percent=statistics.mean(cpu_usage),
            max_cpu_usage_percent=max(cpu_usage),
            total_duration=total_duration,
            requests_per_second=len(successful_results) / total_duration if total_duration > 0 else 0
        )
    
    def save_results(self, output_file: str = "benchmark_results.json"):
        """Save benchmark results to file."""
        output_path = Path(output_file)
        
        # Convert results to dictionaries
        results_data = [asdict(result) for result in self.results]
        
        # Group results by API
        api_results = {}
        for result in self.results:
            api_name = result.api_name
            if api_name not in api_results:
                api_results[api_name] = []
            api_results[api_name].append(result)
        
        # Calculate summaries
        summaries = {}
        for api_name, results in api_results.items():
            if results:
                summaries[api_name] = asdict(self.calculate_summary(results))
        
        # Save to file
        output_data = {
            'timestamp': time.time(),
            'total_results': len(self.results),
            'summaries': summaries,
            'detailed_results': results_data
        }
        
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2, default=str)
        
        print(f"💾 Benchmark results saved to: {output_path}")
        return output_path
    
    def clear_results(self):
        """Clear all stored results."""
        self.results.clear()
    
    def print_summary(self, api_name: Optional[str] = None):
        """Print benchmark summary."""
        if api_name:
            # Print summary for specific API
            api_results = [r for r in self.results if r.api_name == api_name]
            if not api_results:
                print(f"No results found for {api_name}")
                return
            
            summary = self.calculate_summary(api_results)
            self._print_api_summary(summary)
        else:
            # Print summary for all APIs
            api_results = {}
            for result in self.results:
                api_name = result.api_name
                if api_name not in api_results:
                    api_results[api_name] = []
                api_results[api_name].append(result)
            
            for api_name, results in api_results.items():
                if results:
                    summary = self.calculate_summary(results)
                    self._print_api_summary(summary)
                    print()
    
    def _print_api_summary(self, summary: BenchmarkSummary):
        """Print summary for a single API."""
        print(f"📊 {summary.api_name.upper()} BENCHMARK SUMMARY")
        print("=" * 50)
        print(f"Total Requests:     {summary.total_requests}")
        print(f"Successful:         {summary.successful_requests}")
        print(f"Failed:             {summary.failed_requests}")
        print(f"Success Rate:       {summary.success_rate:.1%}")
        print(f"Requests/Second:    {summary.requests_per_second:.2f}")
        print()
        print("Response Times:")
        print(f"  Average:          {summary.avg_response_time:.2f}s")
        print(f"  Minimum:          {summary.min_response_time:.2f}s")
        print(f"  Maximum:          {summary.max_response_time:.2f}s")
        print(f"  50th Percentile:  {summary.p50_response_time:.2f}s")
        print(f"  95th Percentile:  {summary.p95_response_time:.2f}s")
        print(f"  99th Percentile:  {summary.p99_response_time:.2f}s")
        print()
        print("Resource Usage:")
        print(f"  Avg Memory:       {summary.avg_memory_usage_mb:.1f} MB")
        print(f"  Max Memory:       {summary.max_memory_usage_mb:.1f} MB")
        print(f"  Avg CPU:          {summary.avg_cpu_usage_percent:.1f}%")
        print(f"  Max CPU:          {summary.max_cpu_usage_percent:.1f}%")

    def create_performance_charts(self, output_dir: str = "performance_charts"):
        """Create performance visualization charts."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        if not self.results:
            print("No results to visualize")
            return

        # Group results by API
        api_results = {}
        for result in self.results:
            api_name = result.api_name
            if api_name not in api_results:
                api_results[api_name] = []
            api_results[api_name].append(result)

        # Create response time comparison chart
        self._create_response_time_chart(api_results, output_path)

        # Create success rate chart
        self._create_success_rate_chart(api_results, output_path)

        # Create resource usage chart
        self._create_resource_usage_chart(api_results, output_path)

        print(f"📈 Performance charts saved to: {output_path}")

    def _create_response_time_chart(self, api_results: Dict[str, List[BenchmarkResult]], output_path: Path):
        """Create response time comparison chart."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        api_names = list(api_results.keys())
        avg_times = []
        p95_times = []

        for api_name in api_names:
            successful_results = [r for r in api_results[api_name] if r.success]
            if successful_results:
                response_times = [r.response_time for r in successful_results]
                avg_times.append(statistics.mean(response_times))
                p95_times.append(np.percentile(response_times, 95))
            else:
                avg_times.append(0)
                p95_times.append(0)

        # Average response times
        bars1 = ax1.bar(api_names, avg_times, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax1.set_title('Average Response Times', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Response Time (seconds)')
        ax1.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, time_val in zip(bars1, avg_times):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')

        # 95th percentile response times
        bars2 = ax2.bar(api_names, p95_times, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax2.set_title('95th Percentile Response Times', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Response Time (seconds)')
        ax2.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, time_val in zip(bars2, p95_times):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(output_path / 'response_times.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_success_rate_chart(self, api_results: Dict[str, List[BenchmarkResult]], output_path: Path):
        """Create success rate chart."""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        api_names = list(api_results.keys())
        success_rates = []

        for api_name in api_names:
            results = api_results[api_name]
            if results:
                successful = sum(1 for r in results if r.success)
                success_rate = successful / len(results) * 100
                success_rates.append(success_rate)
            else:
                success_rates.append(0)

        bars = ax.bar(api_names, success_rates, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax.set_title('API Success Rates', fontsize=14, fontweight='bold')
        ax.set_ylabel('Success Rate (%)')
        ax.set_ylim(0, 105)
        ax.tick_params(axis='x', rotation=45)

        # Add value labels on bars
        for bar, rate in zip(bars, success_rates):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                   f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

        # Add horizontal line at 95% (target success rate)
        ax.axhline(y=95, color='red', linestyle='--', alpha=0.7, label='Target (95%)')
        ax.legend()

        plt.tight_layout()
        plt.savefig(output_path / 'success_rates.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_resource_usage_chart(self, api_results: Dict[str, List[BenchmarkResult]], output_path: Path):
        """Create resource usage chart."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        api_names = list(api_results.keys())
        avg_memory = []
        max_memory = []
        avg_cpu = []
        max_cpu = []

        for api_name in api_names:
            successful_results = [r for r in api_results[api_name] if r.success]
            if successful_results:
                memory_usage = [r.memory_usage_mb for r in successful_results]
                cpu_usage = [r.cpu_usage_percent for r in successful_results]

                avg_memory.append(statistics.mean(memory_usage))
                max_memory.append(max(memory_usage))
                avg_cpu.append(statistics.mean(cpu_usage))
                max_cpu.append(max(cpu_usage))
            else:
                avg_memory.append(0)
                max_memory.append(0)
                avg_cpu.append(0)
                max_cpu.append(0)

        # Memory usage
        x = np.arange(len(api_names))
        width = 0.35

        bars1 = ax1.bar(x - width/2, avg_memory, width, label='Average', color='#4ECDC4')
        bars2 = ax1.bar(x + width/2, max_memory, width, label='Maximum', color='#FF6B6B')

        ax1.set_title('Memory Usage', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Memory Usage (MB)')
        ax1.set_xticks(x)
        ax1.set_xticklabels(api_names, rotation=45)
        ax1.legend()

        # CPU usage
        bars3 = ax2.bar(x - width/2, avg_cpu, width, label='Average', color='#45B7D1')
        bars4 = ax2.bar(x + width/2, max_cpu, width, label='Maximum', color='#96CEB4')

        ax2.set_title('CPU Usage', fontsize=14, fontweight='bold')
        ax2.set_ylabel('CPU Usage (%)')
        ax2.set_xticks(x)
        ax2.set_xticklabels(api_names, rotation=45)
        ax2.legend()

        plt.tight_layout()
        plt.savefig(output_path / 'resource_usage.png', dpi=300, bbox_inches='tight')
        plt.close()
