#!/usr/bin/env python3
"""
Performance test runner for aibase-ml.

Comprehensive performance testing including benchmarks, load tests,
stress tests, and continuous monitoring.
"""

import argparse
import sys
import time
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

from benchmark_runner import Bench<PERSON><PERSON>unner
from performance_monitor import PerformanceMonitor
from load_tester import LoadTester, LoadTestConfig


class PerformanceTestSuite:
    """Comprehensive performance test suite for aibase-ml."""
    
    def __init__(self):
        self.benchmark_runner = BenchmarkRunner()
        self.performance_monitor = PerformanceMonitor()
        self.load_tester = LoadTester()
        
        # Test results storage
        self.test_results = {
            'benchmarks': {},
            'load_tests': {},
            'stress_tests': {},
            'monitoring_data': {}
        }
    
    def run_benchmark_suite(
        self,
        apis: Optional[List[str]] = None,
        num_requests: int = 10,
        include_concurrent: bool = False
    ) -> Dict[str, Any]:
        """Run comprehensive benchmark tests."""
        print("🔥 Running Performance Benchmark Suite")
        print("=" * 50)
        
        if apis is None:
            apis = list(self.benchmark_runner.api_configs.keys())
        
        benchmark_results = {}
        
        for api_name in apis:
            print(f"\n📊 Benchmarking {api_name}...")
            
            try:
                # Single-threaded benchmark
                single_results = self.benchmark_runner.run_single_api_benchmark(
                    api_name, num_requests
                )
                
                # Concurrent benchmark if requested
                concurrent_results = []
                if include_concurrent:
                    print(f"🚀 Running concurrent benchmark for {api_name}...")
                    concurrent_results = self.benchmark_runner.run_concurrent_benchmark(
                        api_name, num_requests, max_workers=3
                    )
                
                # Calculate summaries
                single_summary = self.benchmark_runner.calculate_summary(single_results)
                concurrent_summary = None
                if concurrent_results:
                    concurrent_summary = self.benchmark_runner.calculate_summary(concurrent_results)
                
                benchmark_results[api_name] = {
                    'single_threaded': {
                        'summary': single_summary,
                        'results': single_results
                    },
                    'concurrent': {
                        'summary': concurrent_summary,
                        'results': concurrent_results
                    } if concurrent_results else None
                }
                
                # Print summary
                print(f"✅ {api_name} benchmark completed:")
                print(f"   Single-threaded: {single_summary.avg_response_time:.2f}s avg, {single_summary.success_rate:.1%} success")
                if concurrent_summary:
                    print(f"   Concurrent: {concurrent_summary.avg_response_time:.2f}s avg, {concurrent_summary.success_rate:.1%} success")
                
            except Exception as e:
                print(f"❌ Failed to benchmark {api_name}: {e}")
                benchmark_results[api_name] = {'error': str(e)}
        
        self.test_results['benchmarks'] = benchmark_results
        return benchmark_results
    
    def run_load_test_suite(
        self,
        apis: Optional[List[str]] = None,
        test_scenarios: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Run load testing scenarios."""
        print("\n🚀 Running Load Test Suite")
        print("=" * 50)
        
        if apis is None:
            apis = list(self.benchmark_runner.api_configs.keys())
        
        if test_scenarios is None:
            test_scenarios = [
                {'name': 'light_load', 'users': 3, 'requests': 5, 'duration': 60},
                {'name': 'medium_load', 'users': 8, 'requests': 8, 'duration': 120},
                {'name': 'heavy_load', 'users': 15, 'requests': 10, 'duration': 180}
            ]
        
        load_test_results = {}
        
        for api_name in apis:
            print(f"\n📈 Load testing {api_name}...")
            api_results = {}
            
            try:
                for scenario in test_scenarios:
                    print(f"   Running {scenario['name']} scenario...")
                    
                    config = LoadTestConfig(
                        api_name=api_name,
                        concurrent_users=scenario['users'],
                        requests_per_user=scenario['requests'],
                        ramp_up_time=10.0,
                        test_duration=scenario['duration'],
                        think_time=2.0
                    )
                    
                    result = self.load_tester.run_load_test(config)
                    api_results[scenario['name']] = result
                    
                    print(f"      ✅ Success rate: {result.success_rate:.1%}, "
                          f"Avg time: {result.avg_response_time:.2f}s, "
                          f"RPS: {result.requests_per_second:.2f}")
                    
                    # Brief pause between scenarios
                    time.sleep(5)
                
                load_test_results[api_name] = api_results
                
            except Exception as e:
                print(f"❌ Failed to load test {api_name}: {e}")
                load_test_results[api_name] = {'error': str(e)}
        
        self.test_results['load_tests'] = load_test_results
        return load_test_results
    
    def run_stress_test_suite(
        self,
        apis: Optional[List[str]] = None,
        max_users: int = 30
    ) -> Dict[str, Any]:
        """Run stress testing to find breaking points."""
        print("\n💪 Running Stress Test Suite")
        print("=" * 50)
        
        if apis is None:
            apis = list(self.benchmark_runner.api_configs.keys())
        
        stress_test_results = {}
        
        for api_name in apis:
            print(f"\n🔥 Stress testing {api_name}...")
            
            try:
                # Run stress test
                stress_results = self.load_tester.run_stress_test(
                    api_name, max_users=max_users, step_size=5
                )
                
                # Find breaking point
                breaking_point = None
                for result in stress_results:
                    if result.success_rate < 0.8 or result.avg_response_time > 60.0:
                        breaking_point = result.config.concurrent_users
                        break
                
                if breaking_point is None and stress_results:
                    breaking_point = stress_results[-1].config.concurrent_users
                
                stress_test_results[api_name] = {
                    'breaking_point': breaking_point,
                    'results': stress_results
                }
                
                print(f"✅ {api_name} stress test completed:")
                print(f"   Breaking point: {breaking_point} concurrent users")
                
            except Exception as e:
                print(f"❌ Failed to stress test {api_name}: {e}")
                stress_test_results[api_name] = {'error': str(e)}
        
        self.test_results['stress_tests'] = stress_test_results
        return stress_test_results
    
    def run_spike_test_suite(
        self,
        apis: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Run spike testing scenarios."""
        print("\n⚡ Running Spike Test Suite")
        print("=" * 50)
        
        if apis is None:
            apis = list(self.benchmark_runner.api_configs.keys())
        
        spike_test_results = {}
        
        for api_name in apis:
            print(f"\n⚡ Spike testing {api_name}...")
            
            try:
                spike_results = self.load_tester.run_spike_test(
                    api_name, baseline_users=3, spike_users=20
                )
                
                spike_test_results[api_name] = spike_results
                
                baseline = spike_results['baseline']
                spike = spike_results['spike']
                
                print(f"✅ {api_name} spike test completed:")
                print(f"   Baseline: {baseline.success_rate:.1%} success, {baseline.avg_response_time:.2f}s avg")
                print(f"   Spike: {spike.success_rate:.1%} success, {spike.avg_response_time:.2f}s avg")
                
            except Exception as e:
                print(f"❌ Failed to spike test {api_name}: {e}")
                spike_test_results[api_name] = {'error': str(e)}
        
        return spike_test_results
    
    def run_monitoring_test(self, duration_minutes: int = 30) -> Dict[str, Any]:
        """Run continuous monitoring test."""
        print(f"\n🔍 Running Monitoring Test ({duration_minutes} minutes)")
        print("=" * 50)
        
        # Set up alert callback
        def alert_callback(alert):
            print(f"🚨 ALERT: {alert.api_name} - {alert.message}")
        
        self.performance_monitor.add_alert_callback(alert_callback)
        
        # Start monitoring
        self.performance_monitor.start_monitoring(interval=10.0)
        
        try:
            # Run some background load during monitoring
            print("🔄 Running background load during monitoring...")
            
            # Light background load on all APIs
            for api_name in self.benchmark_runner.api_configs.keys():
                try:
                    config = LoadTestConfig(
                        api_name=api_name,
                        concurrent_users=2,
                        requests_per_user=duration_minutes * 2,  # 2 requests per minute
                        ramp_up_time=5.0,
                        test_duration=duration_minutes * 60,
                        think_time=30.0  # 30 seconds between requests
                    )
                    
                    # Run in background (don't wait for completion)
                    import threading
                    thread = threading.Thread(
                        target=self.load_tester.run_load_test,
                        args=(config,)
                    )
                    thread.daemon = True
                    thread.start()
                    
                except Exception as e:
                    print(f"⚠️  Failed to start background load for {api_name}: {e}")
            
            # Wait for monitoring period
            print(f"⏱️  Monitoring for {duration_minutes} minutes...")
            time.sleep(duration_minutes * 60)
            
        finally:
            # Stop monitoring
            self.performance_monitor.stop_monitoring()
        
        # Get monitoring results
        monitoring_data = {
            'duration_minutes': duration_minutes,
            'total_metrics': len(self.performance_monitor.metrics),
            'total_alerts': len(self.performance_monitor.alerts),
            'performance_summary': self.performance_monitor.get_performance_summary(),
            'alerts': [alert.__dict__ for alert in self.performance_monitor.alerts]
        }
        
        self.test_results['monitoring_data'] = monitoring_data
        
        print(f"✅ Monitoring test completed:")
        print(f"   Total metrics collected: {monitoring_data['total_metrics']}")
        print(f"   Total alerts generated: {monitoring_data['total_alerts']}")
        
        return monitoring_data
    
    def generate_comprehensive_report(self, output_dir: str = "performance_reports"):
        """Generate comprehensive performance test report."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print(f"\n📊 Generating Comprehensive Performance Report")
        print("=" * 50)
        
        # Save raw results
        results_file = output_path / "performance_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"💾 Raw results saved to: {results_file}")
        
        # Generate visualizations
        try:
            # Benchmark charts
            if self.benchmark_runner.results:
                self.benchmark_runner.create_performance_charts(
                    str(output_path / "benchmark_charts")
                )
            
            # Load test charts
            if self.test_results.get('load_tests'):
                for api_name, scenarios in self.test_results['load_tests'].items():
                    if isinstance(scenarios, dict) and 'error' not in scenarios:
                        scenario_results = list(scenarios.values())
                        if scenario_results:
                            self.load_tester.create_load_test_report(
                                scenario_results,
                                str(output_path / f"{api_name}_load_test.png")
                            )
            
            # Monitoring dashboard
            if self.performance_monitor.metrics:
                self.performance_monitor.create_performance_dashboard(
                    str(output_path / "monitoring_dashboard.png")
                )
            
        except Exception as e:
            print(f"⚠️  Failed to generate some visualizations: {e}")
        
        # Generate summary report
        self._generate_summary_report(output_path)
        
        print(f"📈 Comprehensive report generated in: {output_path}")
    
    def _generate_summary_report(self, output_path: Path):
        """Generate text summary report."""
        summary_file = output_path / "performance_summary.txt"
        
        with open(summary_file, 'w') as f:
            f.write("AIBASE-ML PERFORMANCE TEST SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Test Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Benchmark summary
            if self.test_results.get('benchmarks'):
                f.write("BENCHMARK RESULTS\n")
                f.write("-" * 20 + "\n")
                for api_name, results in self.test_results['benchmarks'].items():
                    if 'error' not in results:
                        single = results['single_threaded']['summary']
                        f.write(f"{api_name}:\n")
                        f.write(f"  Avg Response Time: {single.avg_response_time:.2f}s\n")
                        f.write(f"  Success Rate: {single.success_rate:.1%}\n")
                        f.write(f"  Requests/Second: {single.requests_per_second:.2f}\n\n")
            
            # Load test summary
            if self.test_results.get('load_tests'):
                f.write("LOAD TEST RESULTS\n")
                f.write("-" * 20 + "\n")
                for api_name, scenarios in self.test_results['load_tests'].items():
                    if isinstance(scenarios, dict) and 'error' not in scenarios:
                        f.write(f"{api_name}:\n")
                        for scenario_name, result in scenarios.items():
                            f.write(f"  {scenario_name}: {result.success_rate:.1%} success, ")
                            f.write(f"{result.avg_response_time:.2f}s avg\n")
                        f.write("\n")
            
            # Stress test summary
            if self.test_results.get('stress_tests'):
                f.write("STRESS TEST RESULTS\n")
                f.write("-" * 20 + "\n")
                for api_name, results in self.test_results['stress_tests'].items():
                    if 'error' not in results:
                        breaking_point = results.get('breaking_point', 'Not found')
                        f.write(f"{api_name}: Breaking point at {breaking_point} users\n")
                f.write("\n")
            
            # Monitoring summary
            if self.test_results.get('monitoring_data'):
                monitoring = self.test_results['monitoring_data']
                f.write("MONITORING RESULTS\n")
                f.write("-" * 20 + "\n")
                f.write(f"Duration: {monitoring['duration_minutes']} minutes\n")
                f.write(f"Metrics Collected: {monitoring['total_metrics']}\n")
                f.write(f"Alerts Generated: {monitoring['total_alerts']}\n\n")
        
        print(f"📄 Summary report saved to: {summary_file}")
    
    def cleanup(self):
        """Clean up test resources."""
        self.benchmark_runner.clear_results()
        self.performance_monitor.clear_metrics()
        print("🗑️  Test resources cleaned up")


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Run aibase-ml performance tests')
    
    parser.add_argument(
        '--test-type',
        choices=['benchmark', 'load', 'stress', 'spike', 'monitoring', 'all'],
        default='all',
        help='Type of performance test to run'
    )
    
    parser.add_argument(
        '--apis',
        nargs='+',
        help='Specific APIs to test (default: all)'
    )
    
    parser.add_argument(
        '--requests',
        type=int,
        default=10,
        help='Number of requests for benchmark tests'
    )
    
    parser.add_argument(
        '--max-users',
        type=int,
        default=30,
        help='Maximum users for stress testing'
    )
    
    parser.add_argument(
        '--monitoring-duration',
        type=int,
        default=30,
        help='Monitoring duration in minutes'
    )
    
    parser.add_argument(
        '--output-dir',
        default='performance_reports',
        help='Output directory for reports'
    )
    
    parser.add_argument(
        '--concurrent',
        action='store_true',
        help='Include concurrent benchmarks'
    )
    
    args = parser.parse_args()
    
    # Create test suite
    suite = PerformanceTestSuite()
    
    try:
        print("🧪 aibase-ml Performance Test Suite")
        print("=" * 50)
        
        # Run selected tests
        if args.test_type in ['benchmark', 'all']:
            suite.run_benchmark_suite(
                apis=args.apis,
                num_requests=args.requests,
                include_concurrent=args.concurrent
            )
        
        if args.test_type in ['load', 'all']:
            suite.run_load_test_suite(apis=args.apis)
        
        if args.test_type in ['stress', 'all']:
            suite.run_stress_test_suite(
                apis=args.apis,
                max_users=args.max_users
            )
        
        if args.test_type in ['spike', 'all']:
            suite.run_spike_test_suite(apis=args.apis)
        
        if args.test_type in ['monitoring', 'all']:
            suite.run_monitoring_test(
                duration_minutes=args.monitoring_duration
            )
        
        # Generate comprehensive report
        suite.generate_comprehensive_report(args.output_dir)
        
        print("\n✅ Performance testing completed successfully!")
        
    except KeyboardInterrupt:
        print("\n🛑 Performance testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Performance testing failed: {e}")
        sys.exit(1)
    finally:
        suite.cleanup()


if __name__ == "__main__":
    main()
