"""
Real-time performance monitoring for aibase-ml APIs.

Provides continuous monitoring of API performance with alerting
and historical tracking capabilities.
"""

import time
import psutil
import threading
import queue
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from collections import deque


@dataclass
class PerformanceMetric:
    """Single performance measurement."""
    timestamp: float
    api_name: str
    response_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class PerformanceAlert:
    """Performance alert when thresholds are exceeded."""
    timestamp: float
    api_name: str
    metric_type: str
    threshold: float
    actual_value: float
    severity: str  # 'warning', 'critical'
    message: str


class PerformanceMonitor:
    """Real-time performance monitor for APIs."""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: deque = deque(maxlen=max_history)
        self.alerts: List[PerformanceAlert] = []
        
        # Monitoring configuration
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.metric_queue = queue.Queue()
        
        # Performance thresholds
        self.thresholds = {
            'response_time': {
                'warning': 30.0,    # seconds
                'critical': 60.0    # seconds
            },
            'memory_usage': {
                'warning': 1000.0,  # MB
                'critical': 2000.0  # MB
            },
            'cpu_usage': {
                'warning': 80.0,    # percent
                'critical': 95.0    # percent
            },
            'success_rate': {
                'warning': 0.9,     # 90%
                'critical': 0.8     # 80%
            }
        }
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
        
        # System monitoring
        self.process = psutil.Process()
    
    def set_thresholds(self, thresholds: Dict[str, Dict[str, float]]):
        """Update performance thresholds."""
        self.thresholds.update(thresholds)
    
    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Add callback function for alerts."""
        self.alert_callbacks.append(callback)
    
    def start_monitoring(self, interval: float = 5.0):
        """Start continuous performance monitoring."""
        if self.monitoring:
            print("⚠️  Monitoring already running")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"🔍 Performance monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        print("🛑 Performance monitoring stopped")
    
    def _monitoring_loop(self, interval: float):
        """Main monitoring loop."""
        while self.monitoring:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Process queued API metrics
                self._process_metric_queue()
                
                # Check thresholds and generate alerts
                self._check_thresholds()
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ Monitoring error: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Collect system-level performance metrics."""
        try:
            memory_mb = self.process.memory_info().rss / 1024 / 1024
            cpu_percent = self.process.cpu_percent()
            
            metric = PerformanceMetric(
                timestamp=time.time(),
                api_name='system',
                response_time=0.0,
                memory_usage_mb=memory_mb,
                cpu_usage_percent=cpu_percent,
                success=True
            )
            
            self.metrics.append(metric)
            
        except Exception as e:
            print(f"❌ Failed to collect system metrics: {e}")
    
    def _process_metric_queue(self):
        """Process metrics from the queue."""
        while not self.metric_queue.empty():
            try:
                metric = self.metric_queue.get_nowait()
                self.metrics.append(metric)
            except queue.Empty:
                break
            except Exception as e:
                print(f"❌ Failed to process metric: {e}")
    
    def record_api_call(
        self,
        api_name: str,
        response_time: float,
        success: bool,
        error_message: Optional[str] = None
    ):
        """Record an API call performance metric."""
        try:
            memory_mb = self.process.memory_info().rss / 1024 / 1024
            cpu_percent = self.process.cpu_percent()
        except:
            memory_mb = 0.0
            cpu_percent = 0.0
        
        metric = PerformanceMetric(
            timestamp=time.time(),
            api_name=api_name,
            response_time=response_time,
            memory_usage_mb=memory_mb,
            cpu_usage_percent=cpu_percent,
            success=success,
            error_message=error_message
        )
        
        # Add to queue for thread-safe processing
        self.metric_queue.put(metric)
    
    def _check_thresholds(self):
        """Check performance thresholds and generate alerts."""
        if len(self.metrics) < 2:
            return
        
        # Get recent metrics (last 5 minutes)
        recent_time = time.time() - 300  # 5 minutes
        recent_metrics = [m for m in self.metrics if m.timestamp > recent_time]
        
        if not recent_metrics:
            return
        
        # Group by API
        api_metrics = {}
        for metric in recent_metrics:
            api_name = metric.api_name
            if api_name not in api_metrics:
                api_metrics[api_name] = []
            api_metrics[api_name].append(metric)
        
        # Check thresholds for each API
        for api_name, metrics in api_metrics.items():
            if api_name == 'system':
                continue  # Skip system metrics for API-specific checks
            
            self._check_api_thresholds(api_name, metrics)
    
    def _check_api_thresholds(self, api_name: str, metrics: List[PerformanceMetric]):
        """Check thresholds for a specific API."""
        if not metrics:
            return
        
        # Calculate averages
        response_times = [m.response_time for m in metrics if m.success]
        memory_usage = [m.memory_usage_mb for m in metrics]
        cpu_usage = [m.cpu_usage_percent for m in metrics]
        success_count = sum(1 for m in metrics if m.success)
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            self._check_threshold(
                api_name, 'response_time', avg_response_time,
                f"Average response time: {avg_response_time:.2f}s"
            )
        
        if memory_usage:
            avg_memory = sum(memory_usage) / len(memory_usage)
            self._check_threshold(
                api_name, 'memory_usage', avg_memory,
                f"Average memory usage: {avg_memory:.1f}MB"
            )
        
        if cpu_usage:
            avg_cpu = sum(cpu_usage) / len(cpu_usage)
            self._check_threshold(
                api_name, 'cpu_usage', avg_cpu,
                f"Average CPU usage: {avg_cpu:.1f}%"
            )
        
        # Check success rate
        success_rate = success_count / len(metrics)
        # For success rate, we check if it's BELOW the threshold
        if success_rate < self.thresholds['success_rate']['critical']:
            self._create_alert(
                api_name, 'success_rate', self.thresholds['success_rate']['critical'],
                success_rate, 'critical',
                f"Success rate dropped to {success_rate:.1%}"
            )
        elif success_rate < self.thresholds['success_rate']['warning']:
            self._create_alert(
                api_name, 'success_rate', self.thresholds['success_rate']['warning'],
                success_rate, 'warning',
                f"Success rate dropped to {success_rate:.1%}"
            )
    
    def _check_threshold(self, api_name: str, metric_type: str, value: float, message: str):
        """Check if a value exceeds thresholds."""
        thresholds = self.thresholds.get(metric_type, {})
        
        if value > thresholds.get('critical', float('inf')):
            self._create_alert(
                api_name, metric_type, thresholds['critical'],
                value, 'critical', message
            )
        elif value > thresholds.get('warning', float('inf')):
            self._create_alert(
                api_name, metric_type, thresholds['warning'],
                value, 'warning', message
            )
    
    def _create_alert(
        self,
        api_name: str,
        metric_type: str,
        threshold: float,
        actual_value: float,
        severity: str,
        message: str
    ):
        """Create and process a performance alert."""
        alert = PerformanceAlert(
            timestamp=time.time(),
            api_name=api_name,
            metric_type=metric_type,
            threshold=threshold,
            actual_value=actual_value,
            severity=severity,
            message=message
        )
        
        self.alerts.append(alert)
        
        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                print(f"❌ Alert callback error: {e}")
        
        # Print alert
        severity_icon = "🚨" if severity == 'critical' else "⚠️"
        print(f"{severity_icon} {severity.upper()} ALERT - {api_name}: {message}")
    
    def get_recent_metrics(self, api_name: Optional[str] = None, minutes: int = 60) -> List[PerformanceMetric]:
        """Get recent metrics for analysis."""
        cutoff_time = time.time() - (minutes * 60)
        
        recent_metrics = [m for m in self.metrics if m.timestamp > cutoff_time]
        
        if api_name:
            recent_metrics = [m for m in recent_metrics if m.api_name == api_name]
        
        return recent_metrics
    
    def get_performance_summary(self, api_name: Optional[str] = None, minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for the specified time period."""
        metrics = self.get_recent_metrics(api_name, minutes)
        
        if not metrics:
            return {'error': 'No metrics available'}
        
        # Group by API if no specific API requested
        if api_name is None:
            api_summaries = {}
            api_groups = {}
            
            for metric in metrics:
                if metric.api_name not in api_groups:
                    api_groups[metric.api_name] = []
                api_groups[metric.api_name].append(metric)
            
            for api, api_metrics in api_groups.items():
                api_summaries[api] = self._calculate_summary(api_metrics)
            
            return api_summaries
        else:
            return self._calculate_summary(metrics)
    
    def _calculate_summary(self, metrics: List[PerformanceMetric]) -> Dict[str, Any]:
        """Calculate summary statistics for metrics."""
        if not metrics:
            return {'error': 'No metrics'}
        
        successful_metrics = [m for m in metrics if m.success]
        
        summary = {
            'total_calls': len(metrics),
            'successful_calls': len(successful_metrics),
            'failed_calls': len(metrics) - len(successful_metrics),
            'success_rate': len(successful_metrics) / len(metrics) if metrics else 0,
            'time_period_minutes': (max(m.timestamp for m in metrics) - min(m.timestamp for m in metrics)) / 60
        }
        
        if successful_metrics:
            response_times = [m.response_time for m in successful_metrics]
            memory_usage = [m.memory_usage_mb for m in successful_metrics]
            cpu_usage = [m.cpu_usage_percent for m in successful_metrics]
            
            summary.update({
                'avg_response_time': sum(response_times) / len(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'avg_memory_usage_mb': sum(memory_usage) / len(memory_usage),
                'max_memory_usage_mb': max(memory_usage),
                'avg_cpu_usage_percent': sum(cpu_usage) / len(cpu_usage),
                'max_cpu_usage_percent': max(cpu_usage)
            })
        
        return summary
    
    def save_metrics(self, output_file: str = "performance_metrics.json"):
        """Save metrics to file."""
        output_path = Path(output_file)
        
        data = {
            'timestamp': time.time(),
            'total_metrics': len(self.metrics),
            'total_alerts': len(self.alerts),
            'thresholds': self.thresholds,
            'metrics': [asdict(m) for m in self.metrics],
            'alerts': [asdict(a) for a in self.alerts]
        }
        
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        print(f"💾 Performance metrics saved to: {output_path}")
        return output_path
    
    def create_performance_dashboard(self, output_file: str = "performance_dashboard.png"):
        """Create a performance dashboard visualization."""
        if len(self.metrics) < 2:
            print("Not enough metrics for dashboard")
            return
        
        # Get recent metrics (last hour)
        recent_metrics = self.get_recent_metrics(minutes=60)
        
        if not recent_metrics:
            print("No recent metrics for dashboard")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Convert timestamps to datetime
        timestamps = [datetime.fromtimestamp(m.timestamp) for m in recent_metrics]
        
        # Response times over time
        response_times = [m.response_time for m in recent_metrics if m.success]
        response_timestamps = [datetime.fromtimestamp(m.timestamp) for m in recent_metrics if m.success]
        
        if response_times:
            ax1.plot(response_timestamps, response_times, 'b-', alpha=0.7)
            ax1.set_title('Response Times Over Time')
            ax1.set_ylabel('Response Time (s)')
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax1.tick_params(axis='x', rotation=45)
        
        # Memory usage over time
        memory_usage = [m.memory_usage_mb for m in recent_metrics]
        ax2.plot(timestamps, memory_usage, 'g-', alpha=0.7)
        ax2.set_title('Memory Usage Over Time')
        ax2.set_ylabel('Memory (MB)')
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax2.tick_params(axis='x', rotation=45)
        
        # CPU usage over time
        cpu_usage = [m.cpu_usage_percent for m in recent_metrics]
        ax3.plot(timestamps, cpu_usage, 'r-', alpha=0.7)
        ax3.set_title('CPU Usage Over Time')
        ax3.set_ylabel('CPU (%)')
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax3.tick_params(axis='x', rotation=45)
        
        # Success rate by API
        api_success_rates = {}
        api_groups = {}
        
        for metric in recent_metrics:
            if metric.api_name not in api_groups:
                api_groups[metric.api_name] = []
            api_groups[metric.api_name].append(metric)
        
        for api_name, api_metrics in api_groups.items():
            if api_name != 'system':  # Skip system metrics
                successful = sum(1 for m in api_metrics if m.success)
                success_rate = successful / len(api_metrics) * 100 if api_metrics else 0
                api_success_rates[api_name] = success_rate
        
        if api_success_rates:
            apis = list(api_success_rates.keys())
            rates = list(api_success_rates.values())
            ax4.bar(apis, rates, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
            ax4.set_title('Success Rates by API')
            ax4.set_ylabel('Success Rate (%)')
            ax4.set_ylim(0, 105)
            ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Performance dashboard saved to: {output_file}")
    
    def clear_metrics(self):
        """Clear all stored metrics and alerts."""
        self.metrics.clear()
        self.alerts.clear()
        print("🗑️  Metrics and alerts cleared")
