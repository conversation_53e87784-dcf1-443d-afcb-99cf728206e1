"""
Pytest configuration for integration tests.

Provides fixtures and configuration for integration testing with external services.
"""

import pytest
import os
import time
import requests
from pathlib import Path
import tempfile
import cv2
import numpy as np


def pytest_configure(config):
    """Configure pytest for integration tests."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Add integration marker to all tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add external marker to tests that use external services
        if "external" in item.name or "service" in item.name:
            item.add_marker(pytest.mark.external)
        
        # Add e2e marker to end-to-end tests
        if "e2e" in item.name or "end_to_end" in item.name or "workflow" in item.name:
            item.add_marker(pytest.mark.e2e)
        
        # Add slow marker to tests that are expected to be slow
        if any(keyword in item.name for keyword in ["batch", "concurrent", "workflow", "complete"]):
            item.add_marker(pytest.mark.slow)


@pytest.fixture(scope="session")
def integration_test_config():
    """Configuration for integration tests."""
    return {
        'api_timeout': 60,
        'service_timeout': 30,
        'max_retries': 3,
        'retry_delay': 1,
        'test_image_size': (800, 600),
        'performance_threshold': {
            'segment_apis': 30,  # seconds
            'processing_apis': 60  # seconds
        }
    }


@pytest.fixture(scope="session")
def api_health_check():
    """Check if APIs are running before starting integration tests."""
    api_urls = {
        'main_processing': 'http://localhost:8386/health',
        'segment_auto_crop': 'http://localhost:8388/health',
        'segment_core_pieces': 'http://localhost:8381/health',
        'segment_core_server': 'http://localhost:8389/health'
    }
    
    available_apis = {}
    
    for api_name, health_url in api_urls.items():
        try:
            response = requests.get(health_url, timeout=5)
            available_apis[api_name] = response.status_code == 200
        except:
            available_apis[api_name] = False
    
    return available_apis


@pytest.fixture(scope="session")
def skip_if_apis_unavailable(api_health_check):
    """Skip tests if required APIs are not available."""
    available_count = sum(api_health_check.values())
    if available_count == 0:
        pytest.skip("No APIs are available for integration testing")
    
    return api_health_check


@pytest.fixture(scope="session")
def external_services_available():
    """Check if external services are available."""
    # This would check if API keys are configured and services are reachable
    # For now, we'll assume they're available if environment variables are set
    
    services = {
        'roboflow': bool(os.getenv('ROBOFLOW_API_KEY')),
        'google_vision': bool(os.getenv('GOOGLE_VISION_API_KEY')),
        'gemini': bool(os.getenv('GEMINI_API_KEY')),
        'azure_storage': bool(os.getenv('AZURE_STORAGE_CONNECTION_STRING'))
    }
    
    return services


@pytest.fixture(scope="class")
def sample_core_image():
    """Create a sample core drilling image for testing."""
    # Create a realistic core sample image
    height, width = 800, 600
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add white background
    image.fill(240)
    
    # Add box outline
    box_margin = 40
    cv2.rectangle(image, (box_margin, box_margin), 
                 (width - box_margin, height - box_margin), 
                 (0, 0, 255), 4)
    
    # Add core segments
    segment_height = 60
    num_segments = (height - 2 * box_margin) // segment_height
    
    colors = [
        (139, 69, 19),   # Brown
        (160, 82, 45),   # Saddle brown
        (210, 180, 140), # Tan
        (105, 105, 105), # Dim gray
        (128, 128, 0),   # Olive
    ]
    
    for i in range(num_segments):
        y_start = box_margin + 10 + i * segment_height
        y_end = y_start + segment_height - 10
        
        if y_end > height - box_margin:
            break
        
        color = colors[i % len(colors)]
        
        # Draw segment
        cv2.rectangle(image, (box_margin + 10, y_start), 
                     (width - box_margin - 10, y_end), color, -1)
        
        # Add segment boundary
        cv2.rectangle(image, (box_margin + 10, y_start), 
                     (width - box_margin - 10, y_end), (0, 0, 0), 2)
        
        # Add depth marker
        depth = i * 0.5
        cv2.putText(image, f"{depth:.1f}m", 
                   (width - box_margin + 5, y_start + 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    # Add some text blocks
    text_blocks = [
        (50, height - 80, "Sample: CORE-001"),
        (50, height - 60, "Date: 2024-01-15"),
        (50, height - 40, "Depth: 0-4m"),
    ]
    
    for x, y, text in text_blocks:
        cv2.putText(image, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
        cv2.imwrite(f.name, image)
        return f.name


@pytest.fixture(scope="class")
def sample_box_image():
    """Create a sample box image for testing."""
    height, width = 600, 800
    image = np.full((height, width, 3), 220, dtype=np.uint8)
    
    # Add main box
    box_x, box_y = 100, 50
    box_w, box_h = width - 200, height - 150
    
    # Box outline
    cv2.rectangle(image, (box_x, box_y), (box_x + box_w, box_y + box_h), (255, 0, 0), 4)
    
    # Add pieces inside box
    piece_height = 50
    num_pieces = box_h // piece_height
    
    for i in range(num_pieces - 1):
        y_pos = box_y + 10 + i * piece_height
        
        # Alternate colors
        color = (150, 100, 50) if i % 2 == 0 else (100, 150, 100)
        
        cv2.rectangle(image, (box_x + 10, y_pos), 
                     (box_x + box_w - 10, y_pos + piece_height - 5), color, -1)
        
        # Add piece boundary
        cv2.rectangle(image, (box_x + 10, y_pos), 
                     (box_x + box_w - 10, y_pos + piece_height - 5), (0, 0, 0), 2)
    
    # Add text labels
    for i in range(3):
        x = box_x + 20 + i * 150
        y = box_y + box_h + 30
        cv2.rectangle(image, (x, y), (x + 100, y + 30), (255, 255, 255), -1)
        cv2.rectangle(image, (x, y), (x + 100, y + 30), (0, 0, 0), 2)
        cv2.putText(image, f"Block {i+1}", (x + 10, y + 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
        cv2.imwrite(f.name, image)
        return f.name


@pytest.fixture(scope="function")
def temp_image_cleanup():
    """Clean up temporary images after each test."""
    temp_files = []
    
    def register_temp_file(filepath):
        temp_files.append(filepath)
        return filepath
    
    yield register_temp_file
    
    # Cleanup
    for filepath in temp_files:
        try:
            if os.path.exists(filepath):
                os.unlink(filepath)
        except:
            pass


@pytest.fixture(scope="function")
def performance_monitor():
    """Monitor test performance and collect metrics."""
    start_time = time.time()
    metrics = {
        'start_time': start_time,
        'api_calls': [],
        'errors': []
    }
    
    def record_api_call(api_name, endpoint, duration, status_code):
        metrics['api_calls'].append({
            'api_name': api_name,
            'endpoint': endpoint,
            'duration': duration,
            'status_code': status_code,
            'timestamp': time.time()
        })
    
    def record_error(error_type, message):
        metrics['errors'].append({
            'error_type': error_type,
            'message': message,
            'timestamp': time.time()
        })
    
    metrics['record_api_call'] = record_api_call
    metrics['record_error'] = record_error
    
    yield metrics
    
    # Calculate final metrics
    end_time = time.time()
    metrics['total_duration'] = end_time - start_time
    metrics['total_api_calls'] = len(metrics['api_calls'])
    metrics['total_errors'] = len(metrics['errors'])
    
    if metrics['api_calls']:
        durations = [call['duration'] for call in metrics['api_calls']]
        metrics['avg_api_duration'] = sum(durations) / len(durations)
        metrics['max_api_duration'] = max(durations)
        metrics['min_api_duration'] = min(durations)


def pytest_runtest_setup(item):
    """Setup for each test run."""
    # Skip external service tests if services are not available
    if item.get_closest_marker("external"):
        # Check if external services are configured
        required_services = []
        
        if "roboflow" in item.name:
            required_services.append('ROBOFLOW_API_KEY')
        if "google" in item.name:
            required_services.append('GOOGLE_VISION_API_KEY')
        if "gemini" in item.name:
            required_services.append('GEMINI_API_KEY')
        if "azure" in item.name:
            required_services.append('AZURE_STORAGE_CONNECTION_STRING')
        
        missing_services = [svc for svc in required_services if not os.getenv(svc)]
        
        if missing_services:
            pytest.skip(f"External services not configured: {', '.join(missing_services)}")


def pytest_runtest_teardown(item, nextitem):
    """Teardown after each test run."""
    # Add any cleanup logic here
    pass
