"""
End-to-end integration tests for aibase-ml.

Tests complete workflows from image upload to final results across all APIs.
"""

import pytest
import requests
import json
import time
import os
from pathlib import Path
import tempfile
import cv2
import numpy as np
import base64
from typing import Dict, Any, List


class EndToEndTests:
    """End-to-end integration tests for complete workflows."""
    
    @pytest.fixture(scope="class")
    def api_base_urls(self):
        """Base URLs for all APIs."""
        return {
            'main_processing': 'http://localhost:8386',
            'segment_auto_crop': 'http://localhost:8388', 
            'segment_core_pieces': 'http://localhost:8381',
            'segment_core_server': 'http://localhost:8389'
        }
    
    @pytest.fixture(scope="class")
    def realistic_test_image(self):
        """Create a realistic test image simulating a core drilling sample."""
        # Create a more realistic core sample image
        height, width = 1200, 800
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add gradient background (simulating rock layers)
        for y in range(height):
            intensity = int(120 + 60 * np.sin(y * 0.01))
            image[y, :] = [intensity, intensity - 20, intensity - 40]
        
        # Add box outline (drilling box)
        box_margin = 50
        cv2.rectangle(image, (box_margin, box_margin), 
                     (width - box_margin, height - box_margin), 
                     (0, 0, 255), 8)
        
        # Add core segments with realistic features
        segment_height = 100
        num_segments = (height - 2 * box_margin) // segment_height
        
        for i in range(num_segments):
            y_start = box_margin + i * segment_height + 10
            y_end = y_start + segment_height - 20
            
            # Vary segment colors (different rock types)
            colors = [
                (139, 69, 19),   # Brown
                (160, 82, 45),   # Saddle brown
                (210, 180, 140), # Tan
                (105, 105, 105), # Dim gray
                (128, 128, 0),   # Olive
            ]
            color = colors[i % len(colors)]
            
            # Draw segment
            cv2.rectangle(image, (box_margin + 15, y_start), 
                         (width - box_margin - 15, y_end), color, -1)
            
            # Add texture/noise
            noise = np.random.randint(-30, 30, (y_end - y_start, width - 2 * box_margin - 30, 3))
            segment_region = image[y_start:y_end, box_margin + 15:width - box_margin - 15]
            segment_region = np.clip(segment_region.astype(int) + noise, 0, 255).astype(np.uint8)
            image[y_start:y_end, box_margin + 15:width - box_margin - 15] = segment_region
            
            # Add depth markers
            depth = i * 0.3
            cv2.putText(image, f"{depth:.1f}m", 
                       (width - box_margin + 10, y_start + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # Add geological features
        # Fractures
        for _ in range(3):
            x1 = np.random.randint(box_margin + 20, width - box_margin - 20)
            y1 = np.random.randint(box_margin + 20, height - box_margin - 20)
            x2 = x1 + np.random.randint(-50, 50)
            y2 = y1 + np.random.randint(50, 150)
            cv2.line(image, (x1, y1), (x2, y2), (0, 0, 0), 2)
        
        # Text blocks with geological information
        text_blocks = [
            (100, height - 150, "Sample ID: CORE-2024-001"),
            (100, height - 120, "Location: Site A, Borehole 1"),
            (100, height - 90, "Date: 2024-01-15"),
            (100, height - 60, "Depth Range: 0.0 - 3.0m"),
        ]
        
        for x, y, text in text_blocks:
            # Text background
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            cv2.rectangle(image, (x - 5, y - text_size[1] - 5), 
                         (x + text_size[0] + 5, y + 5), (255, 255, 255), -1)
            cv2.rectangle(image, (x - 5, y - text_size[1] - 5), 
                         (x + text_size[0] + 5, y + 5), (0, 0, 0), 2)
            
            # Text
            cv2.putText(image, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    def wait_for_api(self, base_url: str, timeout: int = 30) -> bool:
        """Wait for API to be available."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{base_url}/health", timeout=5)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    @pytest.mark.integration
    @pytest.mark.e2e
    def test_complete_geological_analysis_workflow(self, api_base_urls, realistic_test_image):
        """Test complete geological analysis workflow using multiple APIs."""
        print("🔬 Starting complete geological analysis workflow...")
        
        workflow_results = {}
        
        # Step 1: Main Processing API - Get overall analysis
        print("📊 Step 1: Running main processing analysis...")
        main_url = api_base_urls['main_processing']
        
        if not self.wait_for_api(main_url):
            pytest.skip("Main processing API not available")
        
        with open(realistic_test_image, 'rb') as f:
            files = {'file': ('core_sample.jpg', f, 'image/jpeg')}
            data = {
                'use_segmentation': 'true',
                'depth_from': '0.0',
                'depth_to': '3.0',
                'custom_prompt': 'Analyze this geological core sample and identify rock types, structures, and any notable features.'
            }
            
            response = requests.post(f"{main_url}/process", files=files, data=data, timeout=120)
        
        assert response.status_code == 200, f"Main processing failed: {response.text}"
        main_result = response.json()
        workflow_results['main_processing'] = main_result
        
        # Validate main processing results
        assert 'warped_image' in main_result, "Main processing should return warped image"
        assert 'google_result' in main_result, "Main processing should return Google OCR results"
        assert 'gemini_result' in main_result, "Main processing should return Gemini AI results"
        
        print(f"✅ Main processing completed - Found {len(main_result.get('x', []))} blocks")
        
        # Step 2: Segment Auto Crop - Get detailed segmentation
        print("🔍 Step 2: Running detailed segmentation...")
        segment_url = api_base_urls['segment_auto_crop']
        
        if not self.wait_for_api(segment_url):
            pytest.skip("Segment auto crop API not available")
        
        with open(realistic_test_image, 'rb') as f:
            files = {'file': ('core_sample.jpg', f, 'image/jpeg')}
            
            response = requests.post(f"{segment_url}/segment_crop", files=files, timeout=60)
        
        assert response.status_code == 200, f"Segmentation failed: {response.text}"
        segment_result = response.json()
        workflow_results['segmentation'] = segment_result
        
        # Validate segmentation results
        assert 'detection' in segment_result, "Segmentation should return detection results"
        predictions = segment_result['detection']['predictions']
        
        print(f"✅ Segmentation completed - Found {len(predictions)} segments")
        
        # Step 3: Core Pieces Analysis - Convert to core pieces
        print("🧱 Step 3: Analyzing core pieces...")
        pieces_url = api_base_urls['segment_core_pieces']
        
        if not self.wait_for_api(pieces_url):
            pytest.skip("Segment core pieces API not available")
        
        with open(realistic_test_image, 'rb') as f:
            files = {'file': ('core_sample.jpg', f, 'image/jpeg')}
            
            response = requests.post(f"{pieces_url}/segment_core_pieces", files=files, timeout=60)
        
        assert response.status_code == 200, f"Core pieces analysis failed: {response.text}"
        pieces_result = response.json()
        workflow_results['core_pieces'] = pieces_result
        
        # Validate core pieces results
        assert 'segmentation' in pieces_result, "Core pieces should return segmentation results"
        core_predictions = pieces_result['segmentation']['predictions']
        
        # Check for Row class conversions
        row_pieces = [pred for pred in core_predictions if pred.get('class') == 'Row']
        print(f"✅ Core pieces analysis completed - Found {len(core_predictions)} total, {len(row_pieces)} rows")
        
        # Step 4: Comprehensive Server Analysis - Full text processing
        print("📝 Step 4: Running comprehensive text analysis...")
        server_url = api_base_urls['segment_core_server']
        
        if not self.wait_for_api(server_url):
            pytest.skip("Segment core server API not available")
        
        with open(realistic_test_image, 'rb') as f:
            files = {'file': ('core_sample.jpg', f, 'image/jpeg')}
            data = {
                'use_row': 'true',
                'depth_from': '0.0',
                'depth_to': '3.0',
                'custom_prompt': 'Extract all geological information including depth measurements, rock types, and structural features.'
            }
            
            response = requests.post(f"{server_url}/process_core_outline", files=files, data=data, timeout=120)
        
        assert response.status_code == 200, f"Server analysis failed: {response.text}"
        server_result = response.json()
        workflow_results['server_analysis'] = server_result
        
        # Validate server results
        assert 'blocks' in server_result, "Server analysis should return blocks"
        blocks = server_result['blocks']
        
        # Check for text processing results
        blocks_with_google = sum(1 for block in blocks if block.get('google_text'))
        blocks_with_gemini = sum(1 for block in blocks if block.get('gemini_text'))
        
        print(f"✅ Server analysis completed - {len(blocks)} blocks, {blocks_with_google} with OCR, {blocks_with_gemini} with AI")
        
        # Step 5: Cross-validate results
        print("🔄 Step 5: Cross-validating results...")
        
        # Compare detection counts across APIs
        main_blocks = len(main_result.get('x', []))
        segment_count = len(predictions)
        pieces_count = len(core_predictions)
        server_blocks = len(blocks)
        
        print(f"📊 Detection counts - Main: {main_blocks}, Segment: {segment_count}, Pieces: {pieces_count}, Server: {server_blocks}")
        
        # Results should be reasonably consistent (within 50% variance)
        counts = [main_blocks, segment_count, pieces_count, server_blocks]
        valid_counts = [c for c in counts if c > 0]
        
        if valid_counts:
            avg_count = sum(valid_counts) / len(valid_counts)
            for count in valid_counts:
                variance = abs(count - avg_count) / avg_count if avg_count > 0 else 0
                assert variance <= 0.5, f"Detection count variance too high: {variance:.2%}"
        
        # Check for text extraction consistency
        if main_result.get('google_result') and blocks_with_google > 0:
            print("✅ Text extraction consistent across APIs")
        
        # Step 6: Generate workflow summary
        workflow_summary = {
            'total_processing_time': sum([
                main_result.get('_metadata', {}).get('processing_time', 0),
                segment_result.get('_metadata', {}).get('processing_time', 0),
                pieces_result.get('_metadata', {}).get('processing_time', 0),
                server_result.get('_metadata', {}).get('processing_time', 0)
            ]),
            'detection_counts': {
                'main_processing': main_blocks,
                'segmentation': segment_count,
                'core_pieces': pieces_count,
                'server_analysis': server_blocks
            },
            'text_processing': {
                'google_ocr_blocks': blocks_with_google,
                'gemini_ai_blocks': blocks_with_gemini,
                'main_google_results': len(main_result.get('google_result', [])),
                'main_gemini_results': len(main_result.get('gemini_result', []))
            },
            'warped_images_generated': sum([
                1 if 'warped_image' in main_result else 0,
                1 if 'warped_image_url' in segment_result else 0,
                1 if 'warped_image_url' in pieces_result else 0,
                1 if 'warped_image_url' in server_result else 0
            ])
        }
        
        print("🎉 Complete geological analysis workflow completed successfully!")
        print(f"📈 Workflow Summary: {json.dumps(workflow_summary, indent=2)}")
        
        return {
            'success': True,
            'workflow_results': workflow_results,
            'summary': workflow_summary
        }
    
    @pytest.mark.integration
    @pytest.mark.e2e
    def test_api_consistency_across_same_image(self, api_base_urls, realistic_test_image):
        """Test that APIs produce consistent results when processing the same image."""
        print("🔄 Testing API consistency across same image...")
        
        results = {}
        
        # Process same image through all APIs
        for api_name, base_url in api_base_urls.items():
            if not self.wait_for_api(base_url):
                continue
            
            print(f"🔍 Processing with {api_name}...")
            
            with open(realistic_test_image, 'rb') as f:
                files = {'file': ('core_sample.jpg', f, 'image/jpeg')}
                
                if api_name == 'main_processing':
                    endpoint = '/process'
                    data = {'use_segmentation': 'false'}  # Faster processing
                elif api_name == 'segment_auto_crop':
                    endpoint = '/segment_crop'
                    data = {}
                elif api_name == 'segment_core_pieces':
                    endpoint = '/segment_core_pieces'
                    data = {}
                elif api_name == 'segment_core_server':
                    endpoint = '/process_core_outline'
                    data = {'use_row': 'false'}  # Faster processing
                
                response = requests.post(f"{base_url}{endpoint}", files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                results[api_name] = response.json()
                print(f"✅ {api_name} completed successfully")
            else:
                print(f"❌ {api_name} failed: {response.status_code}")
        
        # Analyze consistency
        assert len(results) >= 2, "Need at least 2 successful API results for consistency check"
        
        # Check if APIs that should detect similar features do so
        detection_apis = ['segment_auto_crop', 'segment_core_pieces']
        detection_counts = {}
        
        for api in detection_apis:
            if api in results:
                if api == 'segment_auto_crop':
                    count = len(results[api].get('detection', {}).get('predictions', []))
                else:
                    count = len(results[api].get('segmentation', {}).get('predictions', []))
                detection_counts[api] = count
        
        # Detection counts should be reasonably similar
        if len(detection_counts) >= 2:
            counts = list(detection_counts.values())
            max_count = max(counts)
            min_count = min(counts)
            
            if max_count > 0:
                consistency_ratio = min_count / max_count
                assert consistency_ratio >= 0.5, f"Detection consistency too low: {consistency_ratio:.2%}"
                print(f"✅ Detection consistency: {consistency_ratio:.2%}")
        
        return results
    
    @pytest.fixture(autouse=True)
    def cleanup_temp_files(self, realistic_test_image):
        """Clean up temporary test files after tests."""
        yield
        try:
            if os.path.exists(realistic_test_image):
                os.unlink(realistic_test_image)
        except:
            pass
