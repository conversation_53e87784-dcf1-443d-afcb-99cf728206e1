"""
Integration tests for aibase-ml APIs.

This package provides end-to-end integration tests that:
- Test complete API workflows with real images
- Verify external service integrations (Roboflow, Google Vision, Gemini, Azure)
- Test API interactions and data flow
- Validate response formats and processing pipelines
"""

from .test_api_integration import APIIntegrationTests
from .test_external_services import ExternalServicesTests
from .test_end_to_end import EndToEndTests

__all__ = [
    'APIIntegrationTests',
    'ExternalServicesTests', 
    'EndToEndTests'
]
