"""
API integration tests for aibase-ml.

Tests the complete API workflows end-to-end with real images and external services.
"""

import pytest
import requests
import json
import time
import os
from pathlib import Path
from typing import Dict, Any, List
import tempfile
import cv2
import numpy as np
from io import BytesIO
import base64


class APIIntegrationTests:
    """Integration tests for all API endpoints."""
    
    @pytest.fixture(scope="class")
    def api_base_urls(self):
        """Base URLs for all APIs."""
        return {
            'main_processing': 'http://localhost:8386',
            'segment_auto_crop': 'http://localhost:8388', 
            'segment_core_pieces': 'http://localhost:8381',
            'segment_core_server': 'http://localhost:8389'
        }
    
    @pytest.fixture(scope="class")
    def test_image(self):
        """Create a test image for integration testing."""
        # Create a simple test image
        image = np.zeros((600, 400, 3), dtype=np.uint8)
        
        # Add a box outline
        cv2.rectangle(image, (50, 50), (350, 550), (0, 0, 255), 5)
        
        # Add some segments inside
        for i in range(5):
            y_start = 70 + i * 100
            y_end = y_start + 80
            cv2.rectangle(image, (70, y_start), (330, y_end), (100 + i * 30, 150, 100), -1)
            cv2.rectangle(image, (70, y_start), (330, y_end), (0, 0, 0), 2)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    @pytest.fixture(scope="class")
    def sample_images_dir(self):
        """Directory containing sample images."""
        return Path(__file__).parent.parent.parent / "testing" / "sample_images"
    
    def wait_for_api(self, base_url: str, timeout: int = 30) -> bool:
        """Wait for API to be available."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{base_url}/health", timeout=5)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    @pytest.mark.integration
    def test_main_processing_api_integration(self, api_base_urls, test_image):
        """Test main processing API end-to-end."""
        base_url = api_base_urls['main_processing']
        
        # Wait for API to be available
        assert self.wait_for_api(base_url), "Main processing API not available"
        
        # Test with segmentation
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            data = {
                'use_segmentation': 'true',
                'depth_from': '0.0',
                'depth_to': '2.4'
            }
            
            response = requests.post(f"{base_url}/process", files=files, data=data, timeout=60)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        
        # Validate response structure
        required_fields = ['warped_image', 'google_result', 'gemini_result', 'x', 'y', 'width', 'height']
        for field in required_fields:
            assert field in result, f"Missing required field: {field}"
        
        # Validate data types
        assert isinstance(result['warped_image'], str), "warped_image should be base64 string"
        assert isinstance(result['google_result'], list), "google_result should be list"
        assert isinstance(result['gemini_result'], list), "gemini_result should be list"
        assert isinstance(result['x'], list), "x should be list"
        assert isinstance(result['y'], list), "y should be list"
        assert isinstance(result['width'], list), "width should be list"
        assert isinstance(result['height'], list), "height should be list"
        
        # Validate coordinate arrays have same length
        coord_lengths = [len(result[key]) for key in ['x', 'y', 'width', 'height']]
        assert all(length == coord_lengths[0] for length in coord_lengths), "Coordinate arrays should have same length"
        
        # Test warped image can be decoded
        try:
            image_data = base64.b64decode(result['warped_image'])
            assert len(image_data) > 0, "Warped image data should not be empty"
        except Exception as e:
            pytest.fail(f"Failed to decode warped image: {e}")
    
    @pytest.mark.integration
    def test_main_processing_api_custom_prompt(self, api_base_urls, test_image):
        """Test main processing API with custom prompt."""
        base_url = api_base_urls['main_processing']
        
        assert self.wait_for_api(base_url), "Main processing API not available"
        
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            data = {
                'use_segmentation': 'false',
                'custom_prompt': 'Extract depth measurements from this geological core sample'
            }
            
            response = requests.post(f"{base_url}/process", files=files, data=data, timeout=60)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        assert 'gemini_result' in result, "Missing gemini_result field"
        # Custom prompt should potentially generate different results
        # We can't assert specific content but can verify structure
    
    @pytest.mark.integration
    def test_segment_auto_crop_api_integration(self, api_base_urls, test_image):
        """Test segment auto crop API end-to-end."""
        base_url = api_base_urls['segment_auto_crop']
        
        assert self.wait_for_api(base_url), "Segment auto crop API not available"
        
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            
            response = requests.post(f"{base_url}/segment_crop", files=files, timeout=30)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        
        # Validate response structure
        assert 'detection' in result, "Missing detection field"
        assert 'predictions' in result['detection'], "Missing predictions in detection"
        assert isinstance(result['detection']['predictions'], list), "predictions should be list"
        
        # Check if warped image URL is present (optional, depends on Azure upload success)
        if 'warped_image_url' in result:
            assert isinstance(result['warped_image_url'], str), "warped_image_url should be string"
            assert result['warped_image_url'].startswith('http'), "warped_image_url should be valid URL"
        
        # Validate prediction structure
        for prediction in result['detection']['predictions']:
            required_pred_fields = ['class', 'confidence', 'x', 'y', 'width', 'height']
            for field in required_pred_fields:
                assert field in prediction, f"Missing field {field} in prediction"
            
            assert isinstance(prediction['confidence'], (int, float)), "confidence should be numeric"
            assert 0 <= prediction['confidence'] <= 1, "confidence should be between 0 and 1"
    
    @pytest.mark.integration
    def test_segment_core_pieces_api_integration(self, api_base_urls, test_image):
        """Test segment core pieces API end-to-end."""
        base_url = api_base_urls['segment_core_pieces']
        
        assert self.wait_for_api(base_url), "Segment core pieces API not available"
        
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            
            response = requests.post(f"{base_url}/segment_core_pieces", files=files, timeout=30)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        
        # Validate response structure
        assert 'segmentation' in result, "Missing segmentation field"
        assert 'predictions' in result['segmentation'], "Missing predictions in segmentation"
        assert isinstance(result['segmentation']['predictions'], list), "predictions should be list"
        
        # Check for core pieces (should be converted to Row class)
        core_pieces = [pred for pred in result['segmentation']['predictions'] if pred.get('class') == 'Row']
        # Note: We can't assert specific count as it depends on the actual segmentation results
        
        # Validate prediction structure
        for prediction in result['segmentation']['predictions']:
            required_pred_fields = ['class', 'confidence', 'x', 'y', 'width', 'height']
            for field in required_pred_fields:
                assert field in prediction, f"Missing field {field} in prediction"
    
    @pytest.mark.integration
    def test_segment_core_server_api_integration(self, api_base_urls, test_image):
        """Test segment core server API end-to-end."""
        base_url = api_base_urls['segment_core_server']
        
        assert self.wait_for_api(base_url), "Segment core server API not available"
        
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            data = {
                'use_row': 'true',
                'depth_from': '0.0',
                'depth_to': '2.4'
            }
            
            response = requests.post(f"{base_url}/process_core_outline", files=files, data=data, timeout=60)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        
        # Validate response structure
        assert 'blocks' in result, "Missing blocks field"
        assert isinstance(result['blocks'], list), "blocks should be list"
        
        # Validate block structure
        for block in result['blocks']:
            required_block_fields = ['class', 'x', 'y', 'width', 'height', 'confidence']
            for field in required_block_fields:
                assert field in block, f"Missing field {field} in block"
            
            # Check for text processing results (optional, depends on actual processing)
            if 'google_text' in block:
                assert isinstance(block['google_text'], str), "google_text should be string"
            
            if 'gemini_text' in block:
                assert isinstance(block['gemini_text'], str), "gemini_text should be string"
    
    @pytest.mark.integration
    def test_segment_core_server_custom_prompt(self, api_base_urls, test_image):
        """Test segment core server API with custom prompt."""
        base_url = api_base_urls['segment_core_server']
        
        assert self.wait_for_api(base_url), "Segment core server API not available"
        
        with open(test_image, 'rb') as f:
            files = {'file': ('test.jpg', f, 'image/jpeg')}
            data = {
                'use_row': 'true',
                'custom_prompt': 'Identify geological features and rock types in this core sample'
            }
            
            response = requests.post(f"{base_url}/process_core_outline", files=files, data=data, timeout=60)
        
        assert response.status_code == 200, f"API returned {response.status_code}: {response.text}"
        
        result = response.json()
        assert 'blocks' in result, "Missing blocks field"
    
    @pytest.mark.integration
    def test_api_error_handling(self, api_base_urls):
        """Test API error handling with invalid inputs."""
        # Test with invalid file
        for api_name, base_url in api_base_urls.items():
            if not self.wait_for_api(base_url):
                pytest.skip(f"{api_name} API not available")
            
            # Test with non-image file
            files = {'file': ('test.txt', BytesIO(b'not an image'), 'text/plain')}
            
            if api_name == 'main_processing':
                endpoint = '/process'
                data = {'use_segmentation': 'true'}
            elif api_name == 'segment_auto_crop':
                endpoint = '/segment_crop'
                data = {}
            elif api_name == 'segment_core_pieces':
                endpoint = '/segment_core_pieces'
                data = {}
            elif api_name == 'segment_core_server':
                endpoint = '/process_core_outline'
                data = {'use_row': 'true'}
            
            response = requests.post(f"{base_url}{endpoint}", files=files, data=data, timeout=30)
            
            # Should return error status (400 or 500)
            assert response.status_code >= 400, f"{api_name} should return error for invalid file"
    
    @pytest.mark.integration
    def test_api_performance(self, api_base_urls, test_image):
        """Test API performance and response times."""
        performance_results = {}
        
        for api_name, base_url in api_base_urls.items():
            if not self.wait_for_api(base_url):
                pytest.skip(f"{api_name} API not available")
            
            start_time = time.time()
            
            with open(test_image, 'rb') as f:
                files = {'file': ('test.jpg', f, 'image/jpeg')}
                
                if api_name == 'main_processing':
                    endpoint = '/process'
                    data = {'use_segmentation': 'false'}  # Faster without segmentation
                elif api_name == 'segment_auto_crop':
                    endpoint = '/segment_crop'
                    data = {}
                elif api_name == 'segment_core_pieces':
                    endpoint = '/segment_core_pieces'
                    data = {}
                elif api_name == 'segment_core_server':
                    endpoint = '/process_core_outline'
                    data = {'use_row': 'false'}  # Faster without transformation
                
                response = requests.post(f"{base_url}{endpoint}", files=files, data=data, timeout=60)
            
            processing_time = time.time() - start_time
            performance_results[api_name] = processing_time
            
            assert response.status_code == 200, f"{api_name} failed during performance test"
            
            # Performance assertions (adjust based on expected performance)
            if api_name in ['segment_auto_crop', 'segment_core_pieces']:
                assert processing_time < 30, f"{api_name} took too long: {processing_time:.2f}s"
            else:  # APIs with OCR/AI processing
                assert processing_time < 60, f"{api_name} took too long: {processing_time:.2f}s"
        
        print(f"Performance results: {performance_results}")
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_concurrent_requests(self, api_base_urls, test_image):
        """Test APIs can handle concurrent requests."""
        import threading
        import queue
        
        def make_request(api_name, base_url, result_queue):
            try:
                with open(test_image, 'rb') as f:
                    files = {'file': ('test.jpg', f, 'image/jpeg')}
                    
                    if api_name == 'main_processing':
                        endpoint = '/process'
                        data = {'use_segmentation': 'false'}
                    elif api_name == 'segment_auto_crop':
                        endpoint = '/segment_crop'
                        data = {}
                    elif api_name == 'segment_core_pieces':
                        endpoint = '/segment_core_pieces'
                        data = {}
                    elif api_name == 'segment_core_server':
                        endpoint = '/process_core_outline'
                        data = {'use_row': 'false'}
                    
                    response = requests.post(f"{base_url}{endpoint}", files=files, data=data, timeout=60)
                    result_queue.put((api_name, response.status_code, True))
            except Exception as e:
                result_queue.put((api_name, 0, False, str(e)))
        
        # Test 2 concurrent requests per API
        threads = []
        result_queue = queue.Queue()
        
        for api_name, base_url in api_base_urls.items():
            if not self.wait_for_api(base_url):
                continue
            
            for i in range(2):
                thread = threading.Thread(target=make_request, args=(api_name, base_url, result_queue))
                threads.append(thread)
                thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=120)
        
        # Check results
        results = []
        while not result_queue.empty():
            results.append(result_queue.get())
        
        successful_requests = sum(1 for result in results if len(result) >= 3 and result[2])
        total_requests = len(results)
        
        assert successful_requests > 0, "No concurrent requests succeeded"
        success_rate = successful_requests / total_requests
        assert success_rate >= 0.8, f"Concurrent request success rate too low: {success_rate:.2%}"
    
    def teardown_method(self, method):
        """Clean up after each test method."""
        # Clean up any temporary files if needed
        pass

    @pytest.fixture(autouse=True)
    def cleanup_temp_files(self, test_image):
        """Clean up temporary test files after tests."""
        yield
        try:
            if os.path.exists(test_image):
                os.unlink(test_image)
        except:
            pass
