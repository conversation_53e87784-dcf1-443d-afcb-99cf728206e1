"""
External services integration tests for aibase-ml.

Tests integration with external services like Roboflow, Google Vision, Gemini AI, and Azure Storage.
"""

import pytest
import os
import tempfile
import cv2
import numpy as np
from pathlib import Path
import base64
from io import BytesIO
from PIL import Image

# Import services to test
import sys
import time
sys.path.append(str(Path(__file__).parent.parent.parent))

from services.roboflow_service import RoboflowService
from services.google_vision import GoogleVisionService
from services.gemini_service import GeminiService
from services.azure_storage import AzureStorageService
from config.settings import Settings


class ExternalServicesTests:
    """Integration tests for external service dependencies."""
    
    @pytest.fixture(scope="class")
    def settings(self):
        """Load settings for testing."""
        return Settings()
    
    @pytest.fixture(scope="class")
    def test_image_path(self):
        """Create a test image for service testing."""
        # Create a simple test image with text and shapes
        image = np.zeros((400, 600, 3), dtype=np.uint8)
        
        # Add white background
        image.fill(255)
        
        # Add some colored rectangles (simulating core segments)
        cv2.rectangle(image, (50, 50), (550, 150), (100, 150, 200), -1)
        cv2.rectangle(image, (50, 170), (550, 270), (150, 100, 200), -1)
        cv2.rectangle(image, (50, 290), (550, 350), (200, 150, 100), -1)
        
        # Add text
        cv2.putText(image, "Sample Text 123", (100, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, "Depth: 1.5m", (100, 220), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(image, "Core ID: ABC-001", (100, 320), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            cv2.imwrite(f.name, image)
            return f.name
    
    @pytest.fixture(scope="class")
    def test_image_base64(self, test_image_path):
        """Convert test image to base64 for API testing."""
        with open(test_image_path, 'rb') as f:
            image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_roboflow_service_integration(self, settings, test_image_path):
        """Test Roboflow service integration."""
        service = RoboflowService(settings)
        
        # Test box-core-block segmentation
        try:
            result = service.segment_box_core_block(test_image_path)
            
            # Validate response structure
            assert 'predictions' in result, "Missing predictions in Roboflow response"
            assert isinstance(result['predictions'], list), "predictions should be list"
            
            # Validate prediction structure if any predictions exist
            for prediction in result['predictions']:
                required_fields = ['class', 'confidence', 'x', 'y', 'width', 'height']
                for field in required_fields:
                    assert field in prediction, f"Missing field {field} in prediction"
                
                assert isinstance(prediction['confidence'], (int, float)), "confidence should be numeric"
                assert 0 <= prediction['confidence'] <= 1, "confidence should be between 0 and 1"
            
            print(f"Roboflow returned {len(result['predictions'])} predictions")
            
        except Exception as e:
            pytest.skip(f"Roboflow service not available or API key invalid: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_roboflow_custom_model_integration(self, settings, test_image_path):
        """Test Roboflow service with custom model."""
        service = RoboflowService(settings)
        
        try:
            # Test with custom model (if configured)
            result = service.segment_custom_model(test_image_path, "core-pieces-detection")
            
            assert 'predictions' in result, "Missing predictions in custom model response"
            assert isinstance(result['predictions'], list), "predictions should be list"
            
            print(f"Custom model returned {len(result['predictions'])} predictions")
            
        except Exception as e:
            pytest.skip(f"Custom model not available or API key invalid: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_google_vision_service_integration(self, settings, test_image_path):
        """Test Google Vision service integration."""
        service = GoogleVisionService(settings)
        
        try:
            # Test OCR functionality
            result = service.extract_text(test_image_path)
            
            # Validate response structure
            assert 'text_annotations' in result, "Missing text_annotations in Google Vision response"
            assert isinstance(result['text_annotations'], list), "text_annotations should be list"
            
            # Should detect some text from our test image
            if result['text_annotations']:
                first_annotation = result['text_annotations'][0]
                assert 'description' in first_annotation, "Missing description in text annotation"
                assert 'bounding_poly' in first_annotation, "Missing bounding_poly in text annotation"
                
                # Check if it detected our test text
                full_text = first_annotation['description']
                assert len(full_text.strip()) > 0, "Should detect some text"
                
                print(f"Google Vision detected text: {full_text[:100]}...")
            
        except Exception as e:
            pytest.skip(f"Google Vision service not available or API key invalid: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_google_vision_batch_processing(self, settings, test_image_path):
        """Test Google Vision batch processing."""
        service = GoogleVisionService(settings)
        
        try:
            # Test batch processing with multiple images (same image multiple times for testing)
            image_paths = [test_image_path, test_image_path]
            results = service.batch_extract_text(image_paths)
            
            assert isinstance(results, list), "Batch results should be list"
            assert len(results) == len(image_paths), "Should return result for each image"
            
            for result in results:
                assert 'text_annotations' in result, "Each result should have text_annotations"
            
            print(f"Batch processing completed for {len(results)} images")
            
        except Exception as e:
            pytest.skip(f"Google Vision batch processing not available: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_gemini_service_integration(self, settings, test_image_base64):
        """Test Gemini AI service integration."""
        service = GeminiService(settings)
        
        try:
            # Test depth extraction
            result = service.extract_depth_from_image(test_image_base64)
            
            # Validate response
            assert isinstance(result, str), "Gemini result should be string"
            assert len(result.strip()) > 0, "Gemini should return non-empty response"
            
            print(f"Gemini response: {result[:100]}...")
            
        except Exception as e:
            pytest.skip(f"Gemini service not available or API key invalid: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_gemini_custom_prompt_integration(self, settings, test_image_base64):
        """Test Gemini AI service with custom prompt."""
        service = GeminiService(settings)
        
        try:
            custom_prompt = "Describe the geological features visible in this core sample image."
            result = service.process_image_with_prompt(test_image_base64, custom_prompt)
            
            assert isinstance(result, str), "Gemini result should be string"
            assert len(result.strip()) > 0, "Gemini should return non-empty response"
            
            print(f"Gemini custom prompt response: {result[:100]}...")
            
        except Exception as e:
            pytest.skip(f"Gemini custom prompt processing not available: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_gemini_batch_processing(self, settings, test_image_base64):
        """Test Gemini AI batch processing."""
        service = GeminiService(settings)
        
        try:
            # Test batch processing with multiple images
            images = [test_image_base64, test_image_base64]
            results = service.batch_process_images(images)
            
            assert isinstance(results, list), "Batch results should be list"
            assert len(results) == len(images), "Should return result for each image"
            
            for result in results:
                assert isinstance(result, str), "Each result should be string"
                assert len(result.strip()) > 0, "Each result should be non-empty"
            
            print(f"Gemini batch processing completed for {len(results)} images")
            
        except Exception as e:
            pytest.skip(f"Gemini batch processing not available: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_azure_storage_integration(self, settings, test_image_path):
        """Test Azure Storage service integration."""
        service = AzureStorageService(settings)
        
        try:
            # Test image upload
            with open(test_image_path, 'rb') as f:
                image_data = f.read()
            
            # Upload image
            blob_name = f"test_integration_{int(time.time())}.jpg"
            url = service.upload_image(image_data, blob_name)
            
            # Validate URL
            assert isinstance(url, str), "Upload should return URL string"
            assert url.startswith('http'), "Should return valid HTTP URL"
            assert blob_name in url, "URL should contain blob name"
            
            print(f"Azure upload successful: {url}")
            
            # Test image download
            downloaded_data = service.download_image(blob_name)
            assert isinstance(downloaded_data, bytes), "Download should return bytes"
            assert len(downloaded_data) > 0, "Downloaded data should not be empty"
            
            # Verify downloaded data matches original
            assert len(downloaded_data) == len(image_data), "Downloaded size should match original"
            
            # Clean up - delete test blob
            service.delete_image(blob_name)
            print(f"Azure test blob cleaned up: {blob_name}")
            
        except Exception as e:
            pytest.skip(f"Azure Storage service not available or credentials invalid: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_azure_storage_error_handling(self, settings):
        """Test Azure Storage error handling."""
        service = AzureStorageService(settings)
        
        try:
            # Test download of non-existent blob
            with pytest.raises(Exception):
                service.download_image("non_existent_blob.jpg")
            
            # Test upload with invalid data
            with pytest.raises(Exception):
                service.upload_image(None, "test.jpg")
            
        except Exception as e:
            pytest.skip(f"Azure Storage service not available: {e}")
    
    @pytest.mark.integration
    @pytest.mark.external
    def test_service_error_handling(self, settings):
        """Test error handling across all external services."""
        # Test Roboflow with invalid image
        roboflow_service = RoboflowService(settings)
        try:
            with pytest.raises(Exception):
                roboflow_service.segment_box_core_block("non_existent_image.jpg")
        except Exception as e:
            pytest.skip(f"Roboflow service not available: {e}")
        
        # Test Google Vision with invalid image
        google_service = GoogleVisionService(settings)
        try:
            with pytest.raises(Exception):
                google_service.extract_text("non_existent_image.jpg")
        except Exception as e:
            pytest.skip(f"Google Vision service not available: {e}")
        
        # Test Gemini with invalid data
        gemini_service = GeminiService(settings)
        try:
            with pytest.raises(Exception):
                gemini_service.extract_depth_from_image("invalid_base64_data")
        except Exception as e:
            pytest.skip(f"Gemini service not available: {e}")
    
    @pytest.fixture(autouse=True)
    def cleanup_temp_files(self, test_image_path):
        """Clean up temporary test files after tests."""
        yield
        try:
            if os.path.exists(test_image_path):
                os.unlink(test_image_path)
        except:
            pass
