# Integration Tests for aibase-ml

This directory contains comprehensive integration tests for the aibase-ml project. These tests verify that all components work together correctly and that external service integrations function as expected.

## Test Structure

```
tests/integration/
├── README.md                    # This file
├── conftest.py                  # Pytest configuration and fixtures
├── test_api_integration.py      # API endpoint integration tests
├── test_external_services.py    # External service integration tests
└── test_end_to_end.py          # Complete workflow tests
```

## Test Categories

### 1. API Integration Tests (`test_api_integration.py`)
Tests the complete API workflows end-to-end with real images:
- **Main Processing API**: Complete processing pipeline with OCR and AI
- **Segment Auto Crop API**: Segmentation with automatic cropping
- **Segment Core Pieces API**: Core piece extraction and transformation
- **Segment Core Server API**: Block processing with text analysis
- **Error Handling**: Invalid inputs and edge cases
- **Performance Testing**: Response times and concurrent requests

### 2. External Services Tests (`test_external_services.py`)
Tests integration with external services:
- **Roboflow API**: Model inference and segmentation
- **Google Vision API**: OCR text extraction
- **Gemini AI API**: Text processing and analysis
- **Azure Storage**: Image upload/download operations
- **Service Error Handling**: Invalid credentials and network issues

### 3. End-to-End Tests (`test_end_to_end.py`)
Tests complete workflows across multiple APIs:
- **Geological Analysis Workflow**: Complete core sample analysis
- **API Consistency**: Cross-validation of results
- **Multi-step Processing**: Chained API operations
- **Data Flow Validation**: Ensuring data integrity across services

## Test Markers

Integration tests use pytest markers for categorization:

- `@pytest.mark.integration`: All integration tests
- `@pytest.mark.external`: Tests requiring external services
- `@pytest.mark.e2e`: End-to-end workflow tests
- `@pytest.mark.slow`: Long-running tests

## Prerequisites

### 1. Running APIs
All APIs must be running before executing integration tests:

```bash
# Terminal 1 - Main Processing API
cd apis && python main_processing.py

# Terminal 2 - Segment Auto Crop API  
cd apis && python segment_auto_crop.py

# Terminal 3 - Segment Core Pieces API
cd apis && python segment_core_pieces.py

# Terminal 4 - Segment Core Server API
cd apis && python segment_core_server.py
```

### 2. External Service Configuration
For external service tests, configure environment variables:

```bash
# Roboflow API
export ROBOFLOW_API_KEY="your_roboflow_api_key"

# Google Vision API
export GOOGLE_VISION_API_KEY="your_google_vision_api_key"

# Gemini AI API
export GEMINI_API_KEY="your_gemini_api_key"

# Azure Storage
export AZURE_STORAGE_CONNECTION_STRING="your_azure_connection_string"
```

### 3. Dependencies
Install test dependencies:

```bash
pip install -r tests/requirements.txt
```

## Running Integration Tests

### Quick Start
```bash
# Run all integration tests (excluding external services)
python tests/run_integration_tests.py

# Run with verbose output
python tests/run_integration_tests.py --verbose

# Include external service tests
python tests/run_integration_tests.py --include-external

# Include slow tests
python tests/run_integration_tests.py --include-slow
```

### Using pytest directly
```bash
# Run all integration tests
pytest tests/integration/ -m integration

# Run only API tests (no external services)
pytest tests/integration/ -m "integration and not external"

# Run only external service tests
pytest tests/integration/ -m external

# Run only end-to-end tests
pytest tests/integration/ -m e2e

# Run with coverage
pytest tests/integration/ --cov=services --cov=apis --cov-report=html
```

### Test Type Options

#### API Tests Only
```bash
python tests/run_integration_tests.py --type api
```
Tests API endpoints without external service dependencies.

#### External Service Tests
```bash
python tests/run_integration_tests.py --type external --include-external
```
Tests external service integrations (requires API keys).

#### End-to-End Tests
```bash
python tests/run_integration_tests.py --type e2e
```
Tests complete workflows across multiple APIs.

#### All Tests
```bash
python tests/run_integration_tests.py --type all --include-external --include-slow
```
Runs comprehensive test suite (requires all services).

## Test Configuration

### Health Checks
The test runner automatically checks API health before running tests:
- Verifies all APIs are responding
- Skips tests for unavailable APIs
- Reports service status

### Performance Monitoring
Integration tests include performance monitoring:
- API response time tracking
- Concurrent request testing
- Performance threshold validation

### Error Handling
Comprehensive error handling for:
- Network connectivity issues
- Invalid API responses
- External service failures
- Resource cleanup

## Test Data

### Sample Images
Tests use dynamically generated sample images:
- **Core Sample Images**: Realistic geological core samples
- **Box Sample Images**: Clear box boundaries for testing
- **Mixed Sample Images**: Complex multi-element images

### Fixtures
Common test fixtures provide:
- Sample images with known characteristics
- API health monitoring
- Performance metrics collection
- Temporary file cleanup

## Expected Results

### API Integration Tests
- **Response Structure**: Validates required fields and data types
- **Data Consistency**: Ensures coordinate arrays have matching lengths
- **Image Processing**: Verifies warped images can be decoded
- **Text Processing**: Validates OCR and AI text extraction

### External Service Tests
- **Roboflow**: Validates segmentation predictions and confidence scores
- **Google Vision**: Verifies text detection and bounding boxes
- **Gemini AI**: Validates AI-generated text responses
- **Azure Storage**: Tests upload/download operations

### End-to-End Tests
- **Workflow Completion**: All steps execute successfully
- **Data Flow**: Information flows correctly between APIs
- **Result Consistency**: Similar detection counts across APIs
- **Performance**: Processing completes within expected timeframes

## Troubleshooting

### Common Issues

#### APIs Not Responding
```
❌ main_processing: Not responding
```
**Solution**: Ensure all APIs are running on correct ports.

#### External Services Not Configured
```
⚠️ External services requested but none are configured
```
**Solution**: Set required environment variables for API keys.

#### Test Timeouts
```
requests.exceptions.Timeout: Request timed out
```
**Solution**: Increase timeout values or check network connectivity.

#### Image Processing Errors
```
Failed to decode warped image
```
**Solution**: Check image processing pipeline and base64 encoding.

### Debug Mode
Run tests with debug output:
```bash
python tests/run_integration_tests.py --verbose --type api
```

### Health Check Only
Check API status without running tests:
```bash
python tests/run_integration_tests.py --skip-health-check --type api
```

## Continuous Integration

### GitHub Actions
Example workflow for CI:
```yaml
- name: Run Integration Tests
  run: |
    python tests/run_integration_tests.py --type api --output results.xml
  env:
    ROBOFLOW_API_KEY: ${{ secrets.ROBOFLOW_API_KEY }}
    GOOGLE_VISION_API_KEY: ${{ secrets.GOOGLE_VISION_API_KEY }}
```

### Test Reports
Generate test reports:
```bash
# JUnit XML for CI systems
python tests/run_integration_tests.py --output integration_results.xml

# JSON report for analysis
python tests/run_integration_tests.py --report integration_report.json
```

## Performance Benchmarks

### Expected Response Times
- **Segment APIs**: < 30 seconds
- **Processing APIs**: < 60 seconds
- **External Services**: < 10 seconds per call

### Concurrent Request Limits
- **2 concurrent requests per API**: Should maintain 80%+ success rate
- **Memory usage**: Should remain stable during concurrent testing

## Contributing

When adding new integration tests:

1. **Use appropriate markers**: Mark tests with `@pytest.mark.integration` and specific categories
2. **Include cleanup**: Use fixtures for temporary file cleanup
3. **Handle failures gracefully**: Skip tests when services are unavailable
4. **Document expectations**: Clearly document what each test validates
5. **Performance considerations**: Set reasonable timeouts and thresholds

## Support

For issues with integration tests:
1. Check API health status
2. Verify external service configuration
3. Review test logs for specific error messages
4. Ensure all dependencies are installed
5. Check network connectivity to external services
