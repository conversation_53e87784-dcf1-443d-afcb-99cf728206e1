import cv2
import numpy as np
from .detection import find_valid_boxes, assign_box_id_to_rows

def assign_row_index_to_segmentation(segmentation_data, detection_data):
    row_ranges = []
    for detection in detection_data:
        if detection["class"] == "Row":
            min_y = detection["y_raw"] - detection["height"] / 2
            max_y = detection["y_raw"] + detection["height"] / 2
            row_ranges.append({
                "rowIndex": detection["rowIndex"],
                "boxId": detection.get("boxId"),
                "min_y": min_y,
                "max_y": max_y
            })

    row_ranges.sort(key=lambda x: x["min_y"])

    for segment in segmentation_data:
        segment_y_positions = [point[1] for point in segment["points"]]
        min_y = min(segment_y_positions)
        max_y = max(segment_y_positions)
        center_y = (min_y + max_y) / 2

        assigned_row_index = 0
        assigned_box_id = None

        for row in row_ranges:
            if row["min_y"] <= center_y <= row["max_y"]:
                assigned_row_index = row["rowIndex"]
                assigned_box_id = row.get("boxId")
                break

        segment["rowIndex"] = assigned_row_index
        if assigned_box_id:
            segment["boxId"] = assigned_box_id

    return segmentation_data

def polygon_to_mask(image_shape, segmentation_data):
    """ Converts segmentation points into a binary mask """
    mask = np.zeros(image_shape, dtype=np.uint8)
    for segment in segmentation_data:
        points = np.array(segment["points"], dtype=np.int32)
        cv2.fillPoly(mask, [points], 255)
    return mask

def mask_to_points(mask):
    """ Converts a binary mask into a list of (x, y) contour coordinates. """
    mask = mask.astype(np.uint8) * 255  
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    return [contour.reshape(-1, 2).tolist() for contour in contours if len(contour) > 0]

def process_segmentation(response, original_image_path):
    """Processes segmentation results from Roboflow API and maps coordinates to raw image space."""
    if "segmentation" not in response and "segmentation_full" not in response:
        return None
        
    detection_data = response.get("detection", {})

    # Find valid boxes
    valid_boxes = find_valid_boxes(detection_data)
    
    # Assign box IDs to rows
    if valid_boxes:
        detection_data["predictions"] = assign_box_id_to_rows(detection_data, valid_boxes)
    
    processed_segmentations = []

    if response.get("segmentation", []):
        segmentation_data = response["segmentation"]
        use_rescale = True
    else:
        segmentation_data = [response["segmentation_full"]]
        use_rescale = False

    # Process each segmentation object with correct coordinate mapping
    for idx, obj in enumerate(segmentation_data):
        img_w = obj["image"]["width"]
        img_h = obj["image"]["height"]

        if idx < len(valid_boxes):
            box = valid_boxes[idx]
            box_x = int(box["x_raw"] - box["width"] / 2)
            box_y = int(box["y_raw"] - box["height"] / 2)
            box_width = box["width"]
            box_height = box["height"]
            box_id = box["detection_id"]
        else:
            box_x = 0
            box_y = 0
            box_width = 1  # avoid zero division
            box_height = 1
            box_id = None

        for pred in obj.get("predictions", []):
            global_points = [
                (
                    int(box_x + p["x"] * (box_width / img_w)) if use_rescale else int(p["x"]),
                    int(box_y + p["y"] * (box_height / img_h)) if use_rescale else int(p["y"])
                )
                for p in pred["points"]
            ]

            segment = {
                "class": pred["class"],
                "x": pred["x"],
                "y": pred["y"],
                "width": pred["width"],
                "height": pred["height"],
                "confidence": pred["confidence"],
                "detection_id": pred["detection_id"],
                "points": global_points,
            }

            if box_id:
                segment["boxId"] = box_id

            processed_segmentations.append(segment)

    processed_segmentations = assign_row_index_to_segmentation(
        processed_segmentations,
        detection_data.get("predictions", [])
    )

    return processed_segmentations

def process_segment_with_flag(response, original_image_path):
    if "segmentation" not in response:
        return None
        
    detection_data = response.get("detection", {})
    segmentation_data = response["segmentation"]
    
    processed_segmentations = []
    for idx, obj in enumerate(segmentation_data):
        for pred in obj.get("predictions", []):
            global_points = [
                (
                    int(p["x"]),
                    int(p["y"])
                )
                for p in pred["points"]
            ]
    
            segment = {
                "class": pred["class"],
                "x": pred["x"],
                "y": pred["y"],
                "width": pred["width"],
                "height": pred["height"],
                "confidence": pred["confidence"],
                "detection_id": pred["detection_id"],
                "points": global_points,
            }
            processed_segmentations.append(segment)
    
    processed_segmentations = assign_row_index_to_segmentation(
        processed_segmentations,
        detection_data.get("predictions", [])
    )
    return processed_segmentations

def upscale_segmentation_coords(segmentation, width_ratio, height_ratio):
    for seg in segmentation:
        seg["x"] = seg["x"] * width_ratio
        seg["y"] = seg["y"] * height_ratio
        seg["width"] = seg["width"] * width_ratio
        seg["height"] = seg["height"] * height_ratio

        if "points" in seg:
            seg["points"] = [
                (int(x * width_ratio), int(y * height_ratio))
                for x, y in seg["points"]
            ]
    return segmentation