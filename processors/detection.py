def assign_row_index(detection_data):
    """ Assigns row index to "Row" and "Block" detections """
    predictions = detection_data.get("predictions", [])
    row_detections = sorted([pred for pred in predictions if pred["class"] == "Row"], key=lambda x: x["y_raw"])

    row_index_mapping = {}
    for index, row in enumerate(row_detections):
        row["rowIndex"] = index
        row_index_mapping[row["detection_id"]] = index

    for block in predictions:
        if block["class"] == "Block":
            block_y = block["y_raw"]
            closest_row = None
            for row in reversed(row_detections):
                if block_y > row["y_raw"]:
                    closest_row = row
                    break
            block["rowIndex"] = closest_row["rowIndex"] if closest_row else 0  

    return predictions

def find_valid_boxes(detection_data):
    predictions = detection_data.get("predictions", [])
    boxes = [pred for pred in predictions if pred["class"] == "Box"]
    rows = [pred for pred in predictions if pred["class"] == "Row"]
    valid_boxes = []

    for box in boxes:
        box_x = box["x_raw"] - box["width"] / 2
        box_y = box["y_raw"] - box["height"] / 2
        box_right = box_x + box["width"]
        box_bottom = box_y + box["height"]
        rows_in_box = 0
        for row in rows:
            center_x = row["x_raw"]
            center_y = row["y_raw"]
            # Check if the center point is within the box
            if box_x <= center_x <= box_right and box_y <= center_y <= box_bottom:
                rows_in_box += 1

        if rows_in_box > 1:
            box["valid"] = True
            box["rows_count"] = rows_in_box
            valid_boxes.append(box)
        else:
            box["valid"] = False
            box["rows_count"] = rows_in_box

    return valid_boxes

def assign_box_id_to_rows(detection_data, valid_boxes):
    """
    Assigns boxId to rows based on which box they belong to
    """
    predictions = detection_data.get("predictions", [])
    rows = [pred for pred in predictions if pred["class"] == "Row"]
    
    for row in rows:
        row["boxId"] = None
        row_x = row["x_raw"] - row["width"] / 2
        row_y = row["y_raw"] - row["height"] / 2
        row_right = row_x + row["width"]
        row_bottom = row_y + row["height"]
        
        for box_idx, box in enumerate(valid_boxes):
            box_x = box["x_raw"] - box["width"] / 2
            box_y = box["y_raw"] - box["height"] / 2
            box_right = box_x + box["width"]
            box_bottom = box_y + box["height"]
            
            if (box_x <= row_x and row_right <= box_right and 
                box_y <= row_y and row_bottom <= box_bottom):
                row["boxId"] = box["detection_id"]
                break
    
    return predictions

def upscale_detection_coords(predictions, width_ratio, height_ratio):
    for pred in predictions:
        pred["x"] = pred["x"] * width_ratio
        pred["y"] = pred["y"] * height_ratio
        pred["width"] = pred["width"] * width_ratio
        pred["height"] = pred["height"] * height_ratio
        if "x_raw" in pred and "y_raw" in pred:
            pred["x_raw"] = pred["x_raw"] * width_ratio
            pred["y_raw"] = pred["y_raw"] * height_ratio
    return predictions