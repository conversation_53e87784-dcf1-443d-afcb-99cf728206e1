"""Perspective transformation processing for aibase-ml application."""

import cv2
import numpy as np
from typing import List, Dict, Any, <PERSON><PERSON>
from copy import deepcopy

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import ImageProcessingError
from services.image_processing import ImageProcessingService

logger = get_logger(__name__)


class TransformationProcessor:
    """Processor for perspective transformations and segment alignment."""
    
    def __init__(self):
        """Initialize transformation processor."""
        self.settings = get_settings()
        self.image_service = ImageProcessingService()
    
    def apply_box_transform_to_segments(
        self, 
        filter_seg: List[Dict[str, Any]], 
        original_image: np.ndarray,
        extent: int = 10
    ) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """
        Apply perspective transform from the largest 'Box' to all segments.
        
        Args:
            filter_seg: List of filtered segments including the box
            original_image: Original image
            extent: Extension percentage for the box
            
        Returns:
            tuple: (warped_image, transformed_segments)
        """
        try:
            # Find the box segment
            box = next(item for item in filter_seg if item['class'].lower() == 'box')
            others = [item for item in filter_seg if item['class'].lower() != 'box']
            
            # Handle different box representations
            if 'points' in box:
                return self._apply_transform_with_points(box, others, original_image, extent)
            else:
                return self._apply_transform_with_bbox(box, others, original_image, extent)
                
        except StopIteration:
            raise ImageProcessingError("No box segment found for transformation")
        except Exception as e:
            logger.error(f"Failed to apply box transformation: {str(e)}")
            raise ImageProcessingError(
                "Failed to apply perspective transformation",
                details={"error": str(e)}
            )
    
    def _apply_transform_with_points(
        self, 
        box: Dict[str, Any], 
        others: List[Dict[str, Any]], 
        original_image: np.ndarray,
        extent: int
    ) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """Apply transformation using polygon points."""
        hull_points = cv2.convexHull(np.array(box['points'], dtype=np.int32))
        hull_points = self.image_service.scale_contour(hull_points, 1 + extent / 100)
        
        rect = cv2.minAreaRect(hull_points)
        box_points = cv2.boxPoints(rect).astype(np.int32)
        bbox = cv2.boundingRect(box_points)
        bbox = [max(0, x) for x in bbox]
        
        bbox_points = np.array([
            [bbox[0], bbox[1]],
            [bbox[0] + bbox[2], bbox[1]],
            [bbox[0] + bbox[2], bbox[1] + bbox[3]],
            [bbox[0], bbox[1] + bbox[3]]
        ], dtype=np.float32)
        
        epsilon = 0.02 * cv2.arcLength(hull_points, True)
        approx = cv2.approxPolyDP(hull_points, epsilon, True)
        
        src = []
        for bb_point in bbox_points:
            min_dis = float("inf")
            min_point = bb_point
            for point in approx:
                dis = np.linalg.norm(point.ravel() - bb_point)
                if dis < min_dis:
                    min_dis = dis
                    min_point = point.ravel()
            src.append(min_point)
        src = np.array(src, dtype=np.float32)
        
        dst = np.array([[0, 0], [bbox[2], 0], [bbox[2], bbox[3]], [0, bbox[3]]], dtype=np.float32)
        
        M = cv2.getPerspectiveTransform(src, dst)
        warped = cv2.warpPerspective(original_image, M, (bbox[2], bbox[3]))
        
        # Transform all segments
        transformed_segments = []
        for seg in [box] + others:
            points = np.array(seg['points'], dtype=np.float32).reshape(-1, 1, 2)
            warped_pts = cv2.perspectiveTransform(points, M).reshape(-1, 2)
            xs, ys = warped_pts[:, 0], warped_pts[:, 1]
            
            transformed_segments.append({
                'class': seg['class'],
                'confidence': float(seg['confidence']),
                'x': float(np.mean(xs)),
                'y': float(np.mean(ys)),
                'width': float(np.max(xs) - np.min(xs)),
                'height': float(np.max(ys) - np.min(ys)),
                'points': [(int(x), int(y)) for x, y in warped_pts]
            })
        
        return warped, transformed_segments
    
    def _apply_transform_with_bbox(
        self, 
        box: Dict[str, Any], 
        others: List[Dict[str, Any]], 
        original_image: np.ndarray,
        extent: int
    ) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """Apply transformation using bounding box coordinates."""
        # Generate box corner points from bounding box
        box_x_min = int(box['x'] - box['width'] / 2)
        box_x_max = int(box['x'] + box['width'] / 2)
        box_y_min = int(box['y'] - box['height'] / 2)
        box_y_max = int(box['y'] + box['height'] / 2)
        
        box_points = np.array([
            [box_x_min, box_y_min],
            [box_x_max, box_y_min],
            [box_x_max, box_y_max],
            [box_x_min, box_y_max]
        ], dtype=np.int32)
        
        hull_points = self.image_service.scale_contour(box_points, 1 + extent / 100)
        
        rect = cv2.minAreaRect(hull_points)
        box_points = cv2.boxPoints(rect).astype(np.int32)
        bbox = cv2.boundingRect(box_points)
        bbox = [max(0, x) for x in bbox]
        
        bbox_points = np.array([
            [bbox[0], bbox[1]],
            [bbox[0] + bbox[2], bbox[1]],
            [bbox[0] + bbox[2], bbox[1] + bbox[3]],
            [bbox[0], bbox[1] + bbox[3]]
        ], dtype=np.float32)
        
        epsilon = 0.02 * cv2.arcLength(hull_points, True)
        approx = cv2.approxPolyDP(hull_points, epsilon, True)
        
        src = []
        for bb_point in bbox_points:
            min_dis = float("inf")
            min_point = bb_point
            for point in approx:
                dis = np.linalg.norm(point.ravel() - bb_point)
                if dis < min_dis:
                    min_dis = dis
                    min_point = point.ravel()
            src.append(min_point)
        src = np.array(src, dtype=np.float32)
        
        dst = np.array([[0, 0], [bbox[2], 0], [bbox[2], bbox[3]], [0, bbox[3]]], dtype=np.float32)
        
        M = cv2.getPerspectiveTransform(src, dst)
        warped = cv2.warpPerspective(original_image, M, (bbox[2], bbox[3]))
        
        # Transform all segments
        transformed_segments = []
        for seg in [box] + others:
            # Generate corner points from bounding box
            seg_x_min = seg['x'] - seg['width'] / 2
            seg_x_max = seg['x'] + seg['width'] / 2
            seg_y_min = seg['y'] - seg['height'] / 2
            seg_y_max = seg['y'] + seg['height'] / 2
            
            seg_points = np.array([
                [seg_x_min, seg_y_min],
                [seg_x_max, seg_y_min],
                [seg_x_max, seg_y_max],
                [seg_x_min, seg_y_max]
            ], dtype=np.float32).reshape(-1, 1, 2)
            
            warped_pts = cv2.perspectiveTransform(seg_points, M).reshape(-1, 2)
            xs, ys = warped_pts[:, 0], warped_pts[:, 1]
            
            transformed_segments.append({
                'class': seg['class'],
                'confidence': float(seg['confidence']),
                'x': float(np.mean(xs)),
                'y': float(np.mean(ys)),
                'width': float(np.max(xs) - np.min(xs)),
                'height': float(np.max(ys) - np.min(ys)),
            })
        
        return warped, transformed_segments
    
    def update_bounding_boxes_from_points(
        self, 
        segments: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Update bounding box coordinates from polygon points.
        
        Args:
            segments: List of segments with points
            
        Returns:
            list: Segments with updated bounding boxes
        """
        try:
            updated_segments = []
            
            for seg in segments:
                if 'points' not in seg:
                    updated_segments.append(seg)
                    continue
                
                pts = np.array(seg['points'], dtype=np.int32)
                
                x_min = np.min(pts[:, 0])
                y_min = np.min(pts[:, 1])
                x_max = np.max(pts[:, 0])
                y_max = np.max(pts[:, 1])
                
                new_seg = seg.copy()
                new_seg['x'] = float((x_min + x_max) / 2)
                new_seg['y'] = float((y_min + y_max) / 2)
                new_seg['width'] = float(x_max - x_min)
                new_seg['height'] = float(y_max - y_min)
                
                updated_segments.append(new_seg)
            
            return updated_segments

        except Exception as e:
            logger.error(f"Failed to update bounding boxes: {str(e)}")
            raise ImageProcessingError(
                "Failed to update bounding boxes from points",
                details={"error": str(e)}
            )

    def align_core_bounding_boxes_to_box_segment(
        self,
        segments: List[Dict[str, Any]],
        padding: int = 5,
        iou_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """
        Align core segments to the box segment boundaries.

        Args:
            segments: List of segments including box and cores
            padding: Padding from box edges
            iou_threshold: IoU threshold for removing duplicates

        Returns:
            list: Segments with aligned cores
        """
        try:
            def compute_iou(box1: Dict[str, Any], box2: Dict[str, Any]) -> float:
                """Compute IoU between two bounding boxes."""
                x1_min = box1['x'] - box1['width'] / 2
                x1_max = box1['x'] + box1['width'] / 2
                y1_min = box1['y'] - box1['height'] / 2
                y1_max = box1['y'] + box1['height'] / 2

                x2_min = box2['x'] - box2['width'] / 2
                x2_max = box2['x'] + box2['width'] / 2
                y2_min = box2['y'] - box2['height'] / 2
                y2_max = box2['y'] + box2['height'] / 2

                inter_x1 = max(x1_min, x2_min)
                inter_y1 = max(y1_min, y2_min)
                inter_x2 = min(x1_max, x2_max)
                inter_y2 = min(y1_max, y2_max)

                inter_area = max(0, inter_x2 - inter_x1) * max(0, inter_y2 - inter_y1)
                box1_area = (x1_max - x1_min) * (y1_max - y1_min)
                box2_area = (x2_max - x2_min) * (y2_max - y2_min)

                union_area = box1_area + box2_area - inter_area
                return inter_area / union_area if union_area > 0 else 0

            def remove_iou_duplicates(boxes: List[Dict[str, Any]], threshold: float) -> List[Dict[str, Any]]:
                """Remove duplicate boxes based on IoU threshold."""
                unique_boxes = []
                for box in boxes:
                    if not any(compute_iou(box, kept) > threshold for kept in unique_boxes):
                        unique_boxes.append(box)
                return unique_boxes

            # Find box segment
            box_segment = next(seg for seg in segments if seg['class'].lower() == 'box')
            x_left = int(box_segment['x'] - box_segment['width'] / 2) + padding
            x_right = int(box_segment['x'] + box_segment['width'] / 2) - padding

            # Find core segments
            core_segments = [s for s in segments if s['class'].lower() == 'core']
            if not core_segments:
                return segments

            uniform_width = x_right - x_left
            aligned_cores = []

            for seg in core_segments:
                y_center = seg['y']
                height = seg['height']

                aligned_seg = deepcopy(seg)
                aligned_seg['x'] = float((x_left + x_right) / 2)
                aligned_seg['width'] = float(uniform_width)

                # Update points if they exist
                if 'points' in aligned_seg:
                    aligned_seg['points'] = [
                        (x_left, int(y_center - height / 2)),
                        (x_right, int(y_center - height / 2)),
                        (x_right, int(y_center + height / 2)),
                        (x_left, int(y_center + height / 2)),
                    ]

                aligned_cores.append(aligned_seg)

            # Remove duplicates
            aligned_cores = remove_iou_duplicates(aligned_cores, threshold=iou_threshold)
            others = [s for s in segments if s['class'].lower() != 'core']

            logger.info(f"Aligned {len(aligned_cores)} core segments")
            return others + aligned_cores

        except StopIteration:
            logger.warning("No box segment found for alignment")
            return segments
        except Exception as e:
            logger.error(f"Failed to align core segments: {str(e)}")
            raise ImageProcessingError(
                "Failed to align core segments to box",
                details={"error": str(e)}
            )
