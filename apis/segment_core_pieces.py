"""Segment core pieces API for aibase-ml application."""

import cv2
import numpy as np
from flask import Flask, request, jsonify

from config.settings import get_settings
from config.logging import get_logger
from core.decorators import handle_errors, validate_file_upload
from core.response import create_success_response, create_error_response
from services import RoboflowService, AzureStorageService, ImageProcessingService
from processors.transformation import TransformationProcessor

logger = get_logger(__name__)


def create_segment_core_pieces_app():
    """Create and configure the segment core pieces Flask app."""
    app = Flask(__name__)
    settings = get_settings()
    
    # Initialize services
    roboflow_service = RoboflowService()
    azure_service = AzureStorageService()
    image_service = ImageProcessingService()
    transformation_processor = TransformationProcessor()
    
    @app.route("/", methods=["GET"])
    def home():
        """Home endpoint with API documentation."""
        return jsonify({
            "service": "Segment Core Pieces API",
            "version": "1.0.0",
            "description": "Core segmentation with perspective transformation and piece extraction",
            "endpoints": {
                "/segment_core_pieces": {
                    "method": "POST",
                    "description": "Segment image into core pieces with transformation and alignment",
                    "parameters": {
                        "image": "Image file (required)"
                    },
                    "returns": {
                        "segmentation": "Segmentation results with core pieces as rows",
                        "warped_image_url": "Azure URL of warped/transformed image",
                        "warped_image_filename": "Filename of warped image",
                        "processing_info": "Metadata about processing times"
                    }
                },
                "/health": {
                    "method": "GET",
                    "description": "Health check endpoint"
                }
            }
        })
    
    @app.route("/health", methods=["GET"])
    def health():
        """Health check endpoint."""
        return create_success_response({"status": "healthy", "service": "segment_core_pieces"})
    
    @app.route("/segment_core_pieces", methods=["POST"])
    @handle_errors
    @validate_file_upload(file_key='image')
    def segment_image():
        """Segment image into core pieces with transformation."""
        try:
            file = request.files['image']
            
            # Read and decode image
            file_bytes = np.frombuffer(file.read(), np.uint8)
            image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            
            if image is None:
                return create_error_response("Invalid image file", status_code=400)
            
            # Step 1: Run segmentation
            operation, result, detection_time = roboflow_service.run_box_core_block_segmentation(image)
            
            # Step 2: Transform segmentation format
            segments = roboflow_service.transform_segmentation_format(result, include_points=True)
            
            # Step 3: Merge boxes as one segment
            merged_segments = image_service.merge_boxes_as_one_segment(segments)
            
            # Step 4: Find merged box and filter segments inside
            try:
                merged_box = next(seg for seg in merged_segments if seg['class'].lower() == 'box')
                final_segments = image_service.filter_segments_inside_box(merged_segments, merged_box)
            except StopIteration:
                logger.warning("No box segment found, using all segments")
                final_segments = merged_segments
            
            # Step 5: Apply perspective transformation
            warped_image, transformed_seg = transformation_processor.apply_box_transform_to_segments(
                final_segments, image, extent=10
            )
            
            # Step 6: Upload warped image to Azure storage
            warped_image_url = None
            warped_image_filename = None
            upload_error = None
            
            try:
                # Create warped filename
                original_filename = file.filename
                if original_filename:
                    name_without_ext = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                    warped_filename = f"{name_without_ext}_warped.jpg"
                else:
                    warped_filename = None
                
                upload_result = azure_service.upload_warped_image(warped_image, filename=warped_filename)
                
                if upload_result["success"]:
                    warped_image_url = upload_result["url"]
                    warped_image_filename = upload_result["filename"]
                else:
                    upload_error = upload_result["error"]
                    logger.warning(f"Azure upload failed: {upload_error}")
                    
            except Exception as e:
                upload_error = str(e)
                logger.warning(f"Azure upload failed: {upload_error}")
            
            # Step 7: Update bounding boxes and align segments
            transformed_seg = transformation_processor.update_bounding_boxes_from_points(transformed_seg)
            aligned_segments = transformation_processor.align_core_bounding_boxes_to_box_segment(
                transformed_seg,
                padding=150,
                iou_threshold=0.5
            )
            
            # Step 8: Convert core class to Row for consistency
            for item in aligned_segments:
                if item.get("class") == "core":
                    item["class"] = "Row"
            
            # Build response (using 'segmentation' key like original)
            response_data = {
                'segmentation': {'predictions': aligned_segments},
                'processing_info': {
                    'detection_time': detection_time,
                    'operation': operation,
                    'segments_processed': len(aligned_segments)
                }
            }
            
            # Add Azure upload information
            if warped_image_url:
                response_data['warped_image_url'] = warped_image_url
                response_data['warped_image_filename'] = warped_image_filename
            
            if upload_error:
                response_data['upload_error'] = upload_error
            
            logger.info(f"Segment core pieces completed successfully with {len(aligned_segments)} segments")
            return create_success_response(response_data)
            
        except Exception as e:
            logger.error(f"Segment core pieces failed: {str(e)}")
            return create_error_response(
                "Core pieces segmentation failed",
                details={"error": str(e)},
                status_code=500
            )
    
    return app


if __name__ == "__main__":
    app = create_segment_core_pieces_app()
    app.run(host="0.0.0.0", port=8381, debug=True)
