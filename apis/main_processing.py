"""Main processing API for aibase-ml application."""

import os
import uuid
import cv2
import numpy as np
from flask import Flask, request, jsonify

from config.settings import get_settings
from config.logging import get_logger
from core.decorators import handle_errors, validate_file_upload, validate_numeric_params
from core.response import create_success_response, create_error_response
from services import RoboflowService, GoogleVisionService, GeminiService, ImageProcessingService
from utils.image import correct_image_orientation, get_resize_scale_ratio
from processors.detection import assign_row_index, upscale_detection_coords
from processors.segmentation import process_segmentation, process_segment_with_flag, upscale_segmentation_coords

logger = get_logger(__name__)


def create_main_processing_app():
    """Create and configure the main processing Flask app."""
    app = Flask(__name__)
    settings = get_settings()
    
    # Initialize services
    roboflow_service = RoboflowService()
    google_vision_service = GoogleVisionService()
    gemini_service = GeminiService()
    image_service = ImageProcessingService()
    
    def update_google_vision_with_gemini(response, width_ratio, height_ratio):
        """Update Google Vision results with Gemini processing."""
        if "segmentation" not in response or "google_vision_block_ocr" not in response or "google_gemini_block_crop" not in response:
            return response

        # Get all valid boxes
        from processors.detection import find_valid_boxes
        valid_boxes = find_valid_boxes(response['detection'])
        box_mapping = {box["detection_id"]: box for box in valid_boxes}

        # Process segmentation data
        for seg in response["segmentation"]:
            if seg.get("detection_id") in box_mapping:
                box = box_mapping[seg["detection_id"]]
                
                # Find corresponding Google Vision and Gemini results
                google_text = None
                gemini_text = None
                
                for google_result in response["google_vision_block_ocr"]:
                    if google_result.get("detection_id") == seg["detection_id"]:
                        google_text = google_result.get("text", "")
                        break
                
                for gemini_result in response["google_gemini_block_crop"]:
                    if gemini_result.get("detection_id") == seg["detection_id"]:
                        gemini_text = gemini_result.get("text", "")
                        break
                
                # Add text results to segmentation
                seg["google_text"] = google_text or ""
                seg["gemini_text"] = gemini_text or ""
                
                # Scale coordinates
                seg["x"] = seg["x"] * width_ratio
                seg["y"] = seg["y"] * height_ratio
                seg["width"] = seg["width"] * width_ratio
                seg["height"] = seg["height"] * height_ratio

        return response
    
    @app.route("/", methods=["GET"])
    def home():
        """Home endpoint with API documentation."""
        return jsonify({
            "service": "Main Processing API",
            "version": "1.0.0",
            "description": "Complete image processing pipeline with detection, segmentation, OCR, and AI text processing",
            "endpoints": {
                "/process": {
                    "method": "POST",
                    "description": "Process image with Roboflow detection/segmentation, Google OCR, and Gemini AI",
                    "parameters": {
                        "file": "Image file (required)",
                        "depth_from": "Minimum depth value (optional, default: 0)",
                        "depth_to": "Maximum depth value (optional, default: 0)",
                        "prompt": "Custom prompt for Gemini (optional)",
                        "segment_flag": "Enable segmentation processing (optional, default: false)"
                    },
                    "returns": {
                        "detection": "Detection results with row assignments",
                        "segmentation": "Segmentation results with OCR and AI text",
                        "google_vision_block_ocr": "Google Vision OCR results",
                        "google_gemini_block_crop": "Gemini AI processing results",
                        "processing_info": "Metadata about processing times"
                    }
                },
                "/health": {
                    "method": "GET",
                    "description": "Health check endpoint"
                }
            }
        })
    
    @app.route("/health", methods=["GET"])
    def health():
        """Health check endpoint."""
        return create_success_response({"status": "healthy", "service": "main_processing"})
    
    @app.route("/process", methods=["POST"])
    @handle_errors
    @validate_file_upload
    @validate_numeric_params(['depth_from', 'depth_to'])
    def process_image():
        """API endpoint to process an image and apply complete processing pipeline."""
        try:
            # Extract parameters
            depth_from = float(request.form.get("depth_from", 0))
            depth_to = float(request.form.get("depth_to", 0))
            user_prompt = request.form.get("prompt", None)
            segment_flag = request.form.get("segment_flag", "false").lower() in ["true", "1"]
            
            file = request.files["file"]
            file_path = f"./temp_{uuid.uuid4().hex}.jpg"
            file.save(file_path)
            
            # Correct image orientation
            correct_image_orientation(file_path, save_path=file_path)
            
            try:
                # Load image for processing
                image = cv2.imread(file_path)
                if image is None:
                    raise ValueError("Failed to load image")
                
                # Step 1: Run Roboflow inference
                if segment_flag:
                    operation, result, detection_time = roboflow_service.run_box_core_block_segmentation(image)
                else:
                    operation, result, detection_time = roboflow_service.run_box_core_block_segmentation(image)
                
                # Build initial response
                roboflow_response = {
                    "detection": result,
                    "processing_info": {
                        "detection_time": detection_time,
                        "operation": operation
                    }
                }
                
                # Get resize ratios
                width_ratio, height_ratio = get_resize_scale_ratio(file_path)
                
                # Step 2: Assign row indices to detection results
                if "predictions" in roboflow_response["detection"]:
                    roboflow_response["detection"]["predictions"] = assign_row_index(roboflow_response["detection"])
                
                # Step 3: Process segmentation
                if segment_flag:
                    segmentation_result = process_segment_with_flag(roboflow_response, file_path)
                else:
                    segmentation_result = process_segmentation(roboflow_response, file_path)
                
                # Merge segmentation results
                roboflow_response.update(segmentation_result)
                
                # Step 4: Update with Google Vision and Gemini results
                roboflow_response = update_google_vision_with_gemini(roboflow_response, width_ratio, height_ratio)
                
                # Step 5: Upscale coordinates
                roboflow_response["detection"]["predictions"] = upscale_detection_coords(
                    roboflow_response["detection"]["predictions"], width_ratio, height_ratio
                )
                roboflow_response["segmentation"] = upscale_segmentation_coords(
                    roboflow_response["segmentation"], width_ratio, height_ratio
                )
                
                # Step 6: Sort segmentation by row index
                roboflow_response["segmentation"] = sorted(
                    roboflow_response['segmentation'], 
                    key=lambda bbox: bbox.get('rowIndex', 0)
                )
                
                logger.info(f"Main processing completed successfully")
                return create_success_response(roboflow_response)
                
            finally:
                # Clean up temporary file
                if os.path.exists(file_path):
                    os.remove(file_path)
                    
        except Exception as e:
            logger.error(f"Main processing failed: {str(e)}")
            return create_error_response(
                "Processing failed",
                details={"error": str(e)},
                status_code=500
            )
    
    return app


if __name__ == "__main__":
    app = create_main_processing_app()
    app.run(host="0.0.0.0", port=8386, debug=True)
