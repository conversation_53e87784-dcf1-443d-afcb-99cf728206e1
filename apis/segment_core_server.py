"""Segment core server API for aibase-ml application."""

import cv2
import numpy as np
from flask import Flask, request, jsonify
from typing import Optional

from config.settings import get_settings
from config.logging import get_logger
from core.decorators import handle_errors, validate_file_upload
from core.response import create_success_response, create_error_response
from services import (
    RoboflowService, 
    AzureStorageService, 
    ImageProcessingService,
    GoogleVisionService,
    GeminiService
)
from processors.transformation import TransformationProcessor

logger = get_logger(__name__)


def create_segment_core_server_app():
    """Create and configure the segment core server Flask app."""
    app = Flask(__name__)
    settings = get_settings()
    
    # Initialize services
    roboflow_service = RoboflowService()
    azure_service = AzureStorageService()
    image_service = ImageProcessingService()
    google_vision_service = GoogleVisionService()
    gemini_service = GeminiService()
    transformation_processor = TransformationProcessor()
    
    @app.route("/", methods=["GET"])
    def home():
        """Home endpoint with API documentation."""
        return jsonify({
            "service": "Segment Core Server API",
            "version": "1.0.0",
            "description": "Complete segmentation pipeline with Google OCR and Gemini processing",
            "endpoints": {
                "/process_core_outline": {
                    "method": "POST",
                    "description": "Process image and return Block segments with integrated Google OCR and Gemini results",
                    "parameters": {
                        "image": "Image file (required)",
                        "use_row": "Boolean to apply row transformation (optional, default: true)",
                        "depth_from": "Minimum depth value (optional, default: 0)",
                        "depth_to": "Maximum depth value (optional, default: 2.4)",
                        "custom_prompt": "Custom prompt for Gemini (optional)"
                    },
                    "returns": {
                        "blocks": "Array of Block objects with x, y, width, height, confidence, google_text, gemini_text",
                        "warped_image_url": "Azure URL of warped image (if use_row=true)",
                        "processing_info": "Metadata about processing times and parameters"
                    }
                },
                "/health": {
                    "method": "GET",
                    "description": "Health check endpoint"
                }
            }
        })
    
    @app.route("/health", methods=["GET"])
    def health():
        """Health check endpoint."""
        return create_success_response({"status": "healthy", "service": "segment_core_server"})
    
    @app.route("/process_core_outline", methods=["POST"])
    @handle_errors
    @validate_file_upload(file_key='image')
    def process_core_outline():
        """Main endpoint to process image and return all results including warped image, Google OCR, and Gemini results."""
        try:
            file = request.files['image']
            
            # Get optional parameters
            use_row = request.form.get('use_row', 'true').lower() == 'true'
            depth_from = float(request.form.get('depth_from', 0))
            depth_to = float(request.form.get('depth_to', 2.4))
            custom_prompt = request.form.get('custom_prompt', None)
            
            # Read and decode image
            file_bytes = np.frombuffer(file.read(), np.uint8)
            image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
            
            if image is None:
                return create_error_response("Invalid image file", status_code=400)
            
            # Step 1: Run segmentation
            operation, result, detection_time = roboflow_service.run_box_core_block_segmentation(image)
            
            # Step 2: Transform segmentation format
            segments = roboflow_service.transform_segmentation_format(result, include_points=True)
            
            # Step 3: Merge boxes as one segment
            merged_segments = image_service.merge_boxes_as_one_segment(segments)
            
            # Step 4: Find merged box and filter segments inside
            try:
                merged_box = next(seg for seg in merged_segments if seg['class'].lower() == 'box')
                final_segments = image_service.filter_segments_inside_box(merged_segments, merged_box)
            except StopIteration:
                logger.warning("No box segment found, using all segments")
                final_segments = merged_segments
            
            # Step 5: Apply transformation if use_row is True
            warped_image = None
            transformed_seg = final_segments
            warped_image_url = None
            warped_image_filename = None
            upload_error = None
            
            if use_row:
                try:
                    warped_image, transformed_seg = transformation_processor.apply_box_transform_to_segments(
                        final_segments, image, extent=10
                    )
                    
                    # Upload warped image to Azure
                    original_filename = file.filename
                    if original_filename:
                        name_without_ext = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                        warped_filename = f"{name_without_ext}_warped.jpg"
                    else:
                        warped_filename = None
                    
                    upload_result = azure_service.upload_warped_image(warped_image, filename=warped_filename)
                    
                    if upload_result["success"]:
                        warped_image_url = upload_result["url"]
                        warped_image_filename = upload_result["filename"]
                    else:
                        upload_error = upload_result["error"]
                        logger.warning(f"Azure upload failed: {upload_error}")
                        
                except Exception as e:
                    upload_error = str(e)
                    logger.warning(f"Transformation or upload failed: {upload_error}")
            
            # Step 6: Update bounding boxes and align segments
            if 'points' in transformed_seg[0] if transformed_seg else False:
                transformed_seg = transformation_processor.update_bounding_boxes_from_points(transformed_seg)
            
            aligned_segments = transformation_processor.align_core_bounding_boxes_to_box_segment(
                transformed_seg,
                padding=150,
                iou_threshold=0.5
            )
            
            # Step 7: Extract Block segments for OCR and Gemini processing
            block_segments = [seg for seg in aligned_segments if seg['class'].lower() == 'block']
            
            if block_segments:
                # Crop block images for processing
                source_image = warped_image if warped_image is not None else image
                cropped_images = image_service.crop_boxes_from_segments(
                    block_segments, 
                    source_image,
                    target_class="Block",
                    y_tolerance=50,
                    padding=10
                )
                
                # Step 8: Run Google Vision OCR
                google_operation = None
                google_results = []
                google_time = 0
                
                if cropped_images:
                    try:
                        google_operation, google_results, google_time = google_vision_service.run_ocr_on_cropped_images(cropped_images)
                    except Exception as e:
                        logger.warning(f"Google Vision OCR failed: {str(e)}")
                        google_results = [f"Error: {str(e)}" for _ in cropped_images]
                
                # Step 9: Run Gemini processing
                gemini_operation = None
                gemini_results = []
                gemini_time = 0
                
                if cropped_images:
                    try:
                        if custom_prompt:
                            gemini_operation, gemini_results, gemini_time = gemini_service.run_prompt_on_cropped_images(
                                cropped_images, custom_prompt
                            )
                        else:
                            gemini_operation, gemini_results, gemini_time = gemini_service.run_depth_extraction(
                                cropped_images, depth_from, depth_to
                            )
                    except Exception as e:
                        logger.warning(f"Gemini processing failed: {str(e)}")
                        gemini_results = [f"Error: {str(e)}" for _ in cropped_images]
                
                # Step 10: Combine results with block segments
                for i, block in enumerate(block_segments):
                    if i < len(google_results):
                        block['google_text'] = google_results[i]
                    else:
                        block['google_text'] = ""
                    
                    if i < len(gemini_results):
                        block['gemini_text'] = gemini_results[i]
                    else:
                        block['gemini_text'] = ""
            
            # Build response
            response_data = {
                'blocks': block_segments,
                'processing_info': {
                    'detection_time': detection_time,
                    'google_ocr_time': google_time,
                    'gemini_time': gemini_time,
                    'operation': operation,
                    'google_operation': google_operation,
                    'gemini_operation': gemini_operation,
                    'use_row': use_row,
                    'depth_range': [depth_from, depth_to],
                    'blocks_processed': len(block_segments)
                }
            }
            
            # Add Azure upload information
            if warped_image_url:
                response_data['warped_image_url'] = warped_image_url
                response_data['warped_image_filename'] = warped_image_filename
            
            if upload_error:
                response_data['upload_error'] = upload_error
            
            logger.info(f"Core outline processing completed successfully with {len(block_segments)} blocks")
            return create_success_response(response_data)
            
        except Exception as e:
            logger.error(f"Core outline processing failed: {str(e)}")
            return create_error_response(
                "Core outline processing failed",
                details={"error": str(e)},
                status_code=500
            )
    
    return app


if __name__ == "__main__":
    app = create_segment_core_server_app()
    app.run(host="0.0.0.0", port=8389, debug=True)
