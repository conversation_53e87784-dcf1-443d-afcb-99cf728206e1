"""Image processing service for aibase-ml application."""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from copy import deepcopy

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import ImageProcessingError

logger = get_logger(__name__)


class ImageProcessingService:
    """Service for common image processing operations."""
    
    def __init__(self):
        """Initialize image processing service."""
        self.settings = get_settings()
    
    def scale_contour(self, contour: np.ndarray, scale: float) -> np.ndarray:
        """
        Scale a contour around its center.
        
        Args:
            contour: Input contour as numpy array
            scale: Scale factor
            
        Returns:
            np.ndarray: Scaled contour
        """
        try:
            M = cv2.moments(contour)
            if M['m00'] == 0:
                return contour
            
            cx = int(M['m10'] / M['m00'])
            cy = int(M['m01'] / M['m00'])
            center = np.array([[cx, cy]])
            scaled = (contour - center) * scale + center
            return scaled.astype(np.int32)
            
        except Exception as e:
            logger.error(f"Failed to scale contour: {str(e)}")
            raise ImageProcessingError(
                "Failed to scale contour",
                details={"error": str(e)}
            )
    
    def merge_boxes_as_one_segment(
        self, 
        segments: List[Dict[str, Any]], 
        merged_class: str = 'Box'
    ) -> List[Dict[str, Any]]:
        """
        Merge all 'Box' segments into a single large segment.
        
        Args:
            segments: List of segment dictionaries
            merged_class: Class name for the merged segment
            
        Returns:
            list: Segments with boxes merged
        """
        try:
            box_segments = [seg for seg in segments if seg['class'].lower() == 'box']
            other_segments = [seg for seg in segments if seg['class'].lower() != 'box']
            
            if not box_segments:
                return segments
            
            # Method 1: Using points if available
            if all('points' in seg for seg in box_segments):
                return self._merge_boxes_with_points(box_segments, other_segments, merged_class)
            
            # Method 2: Using bounding boxes
            return self._merge_boxes_with_bounding_boxes(box_segments, other_segments, merged_class)
            
        except Exception as e:
            logger.error(f"Failed to merge box segments: {str(e)}")
            raise ImageProcessingError(
                "Failed to merge box segments",
                details={"error": str(e)}
            )
    
    def _merge_boxes_with_points(
        self, 
        box_segments: List[Dict[str, Any]], 
        other_segments: List[Dict[str, Any]], 
        merged_class: str
    ) -> List[Dict[str, Any]]:
        """Merge boxes using convex hull of points."""
        all_points = []
        confidences = []
        
        for seg in box_segments:
            all_points.extend(seg['points'])
            confidences.append(seg['confidence'])
        
        all_points_np = np.array(all_points, dtype=np.int32)
        merged_hull = cv2.convexHull(all_points_np)
        merged_points = [(int(pt[0][0]), int(pt[0][1])) for pt in merged_hull]
        
        x_vals = [pt[0] for pt in merged_points]
        y_vals = [pt[1] for pt in merged_points]
        x_min, x_max = min(x_vals), max(x_vals)
        y_min, y_max = min(y_vals), max(y_vals)
        
        merged_segment = {
            "class": merged_class,
            "confidence": float(np.mean(confidences)),
            "x": float((x_min + x_max) / 2),
            "y": float((y_min + y_max) / 2),
            "width": float(x_max - x_min),
            "height": float(y_max - y_min),
            "points": merged_points
        }
        
        return other_segments + [merged_segment]
    
    def _merge_boxes_with_bounding_boxes(
        self, 
        box_segments: List[Dict[str, Any]], 
        other_segments: List[Dict[str, Any]], 
        merged_class: str
    ) -> List[Dict[str, Any]]:
        """Merge boxes using bounding box union."""
        confidences = []
        x_mins, y_mins, x_maxs, y_maxs = [], [], [], []
        
        for seg in box_segments:
            confidences.append(seg['confidence'])
            x_min = seg['x'] - seg['width'] / 2
            x_max = seg['x'] + seg['width'] / 2
            y_min = seg['y'] - seg['height'] / 2
            y_max = seg['y'] + seg['height'] / 2
            
            x_mins.append(x_min)
            x_maxs.append(x_max)
            y_mins.append(y_min)
            y_maxs.append(y_max)
        
        x_min, x_max = min(x_mins), max(x_maxs)
        y_min, y_max = min(y_mins), max(y_maxs)
        
        merged_segment = {
            "class": merged_class,
            "confidence": float(np.mean(confidences)),
            "x": float((x_min + x_max) / 2),
            "y": float((y_min + y_max) / 2),
            "width": float(x_max - x_min),
            "height": float(y_max - y_min),
        }
        
        return other_segments + [merged_segment]
    
    def filter_segments_inside_box(
        self, 
        segments: List[Dict[str, Any]], 
        merged_box_segment: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Filter segments that are inside the merged box segment.
        
        Args:
            segments: List of all segments
            merged_box_segment: The merged box segment
            
        Returns:
            list: Filtered segments
        """
        try:
            filtered_segments = []
            
            # Use points if available, otherwise use bounding box
            if 'points' in merged_box_segment:
                box_contour = np.array(merged_box_segment['points'], dtype=np.int32)
                
                for seg in segments:
                    if seg is merged_box_segment:
                        filtered_segments.append(seg)
                        continue
                    
                    center = (int(seg['x']), int(seg['y']))
                    if cv2.pointPolygonTest(box_contour, center, False) >= 0:
                        filtered_segments.append(seg)
            else:
                # Use bounding box filtering
                box_x_min = merged_box_segment['x'] - merged_box_segment['width'] / 2
                box_x_max = merged_box_segment['x'] + merged_box_segment['width'] / 2
                box_y_min = merged_box_segment['y'] - merged_box_segment['height'] / 2
                box_y_max = merged_box_segment['y'] + merged_box_segment['height'] / 2
                
                for seg in segments:
                    if seg is merged_box_segment:
                        filtered_segments.append(seg)
                        continue
                    
                    seg_x, seg_y = seg['x'], seg['y']
                    if (box_x_min <= seg_x <= box_x_max and
                        box_y_min <= seg_y <= box_y_max):
                        filtered_segments.append(seg)
            
            logger.info(f"Filtered {len(filtered_segments)} segments inside box")
            return filtered_segments
            
        except Exception as e:
            logger.error(f"Failed to filter segments: {str(e)}")
            raise ImageProcessingError(
                "Failed to filter segments inside box",
                details={"error": str(e)}
            )
    
    def crop_boxes_from_segments(
        self, 
        segments: List[Dict[str, Any]], 
        image: np.ndarray,
        target_class: str = "Block",
        y_tolerance: int = 50,
        padding: int = 10
    ) -> List[np.ndarray]:
        """
        Extract and sort cropped regions from segments.
        
        Args:
            segments: List of segment dictionaries
            image: Source image
            target_class: Class to crop (e.g., "Block")
            y_tolerance: Tolerance for grouping by rows
            padding: Padding around cropped regions
            
        Returns:
            list: List of cropped images sorted by position
        """
        try:
            box_entries = []
            
            for seg in segments:
                if seg.get("class") == target_class:
                    x = int(seg['x'] - seg['width'] / 2)
                    y = int(seg['y'] - seg['height'] / 2)
                    w = int(seg['width'])
                    h = int(seg['height'])
                    
                    x_end = min(x + w, image.shape[1])
                    y_end = min(y + h, image.shape[0])
                    x_start = max(x, 0)
                    y_start = max(y, 0)
                    
                    # Apply padding
                    x_start_padded = max(x_start - padding, 0)
                    y_start_padded = max(y_start - padding, 0)
                    x_end_padded = min(x_end + padding, image.shape[1])
                    y_end_padded = min(y_end + padding, image.shape[0])
                    
                    cropped = image[y_start_padded:y_end_padded, x_start_padded:x_end_padded].copy()
                    
                    box_entries.append({
                        "x": int(seg['x']),
                        "y": int(seg['y']),
                        "image": cropped
                    })
            
            # Sort by y (top to bottom)
            box_entries.sort(key=lambda entry: entry["y"])
            
            # Group by rows using y_tolerance
            rows = []
            current_row = []
            last_y = None
            
            for entry in box_entries:
                if last_y is None or abs(entry["y"] - last_y) <= y_tolerance:
                    current_row.append(entry)
                else:
                    rows.append(current_row)
                    current_row = [entry]
                last_y = entry["y"]
            
            if current_row:
                rows.append(current_row)
            
            # Sort each row by x (left to right)
            sorted_images = []
            for row in rows:
                row_sorted = sorted(row, key=lambda entry: entry["x"])
                sorted_images.extend([entry["image"] for entry in row_sorted])
            
            logger.info(f"Cropped {len(sorted_images)} {target_class} segments")
            return sorted_images
            
        except Exception as e:
            logger.error(f"Failed to crop segments: {str(e)}")
            raise ImageProcessingError(
                "Failed to crop segments from image",
                details={"error": str(e), "target_class": target_class}
            )
