"""Roboflow service for aibase-ml application."""

import time
import numpy as np
from typing import Dict, Any, Tu<PERSON>, Optional
from inference import get_roboflow_model

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import RoboflowAPIError, ModelInferenceError

logger = get_logger(__name__)


class RoboflowService:
    """Service for handling Roboflow model inference."""
    
    def __init__(self):
        """Initialize Roboflow service."""
        self.settings = get_settings()
        self._models = {}  # Cache for loaded models
    
    def _get_model(self, model_id: str):
        """Get or load a Roboflow model."""
        if model_id not in self._models:
            try:
                logger.info(f"Loading Roboflow model: {model_id}")
                self._models[model_id] = get_roboflow_model(
                    model_id=model_id, 
                    api_key=self.settings.roboflow_api_key
                )
            except Exception as e:
                raise RoboflowAPIError(
                    f"Failed to load Roboflow model: {model_id}",
                    details={"model_id": model_id, "error": str(e)}
                )
        return self._models[model_id]
    
    def run_box_core_block_segmentation(
        self, 
        image: np.ndarray,
        model_version: str = "v7"
    ) -> Tuple[str, Dict[str, Any], float]:
        """
        Run box-core-block segmentation on an image.
        
        Args:
            image: Input image as numpy array
            model_version: Model version to use ("v6" or "v7")
            
        Returns:
            tuple: (operation_name, result_dict, elapsed_time)
        """
        try:
            # Select model ID based on version
            if model_version == "v6":
                model_id = self.settings.box_core_block_model_id_v6
            else:
                model_id = self.settings.box_core_block_model_id
            
            start_time = time.time()
            model = self._get_model(model_id)
            
            logger.info(f"Running inference with model {model_id}")
            result = model.infer(image)
            elapsed_time = time.time() - start_time
            
            logger.info(f"Inference completed in {elapsed_time:.3f}s")
            
            return "Detection", result[0].dict(), elapsed_time
            
        except RoboflowAPIError:
            raise
        except Exception as e:
            logger.error(f"Model inference failed: {str(e)}")
            raise ModelInferenceError(
                "Failed to run box-core-block segmentation",
                details={"model_version": model_version, "error": str(e)}
            )
    
    def transform_segmentation_format(
        self, 
        segmentation_data: Dict[str, Any],
        include_points: bool = True,
        confidence_threshold: Optional[float] = None
    ) -> list:
        """
        Transform segmentation data to standardized format.
        
        Args:
            segmentation_data: Raw segmentation data from Roboflow
            include_points: Whether to include polygon points in output
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            list: Processed segmentation data
        """
        if confidence_threshold is None:
            confidence_threshold = self.settings.confidence_threshold
        
        processed_segmentations = []
        
        for pred in segmentation_data.get("predictions", []):
            if pred['confidence'] < confidence_threshold:
                continue
            
            segment = {
                "class": pred["class_name"],
                "x": float(pred["x"]),
                "y": float(pred["y"]),
                "width": float(pred["width"]),
                "height": float(pred["height"]),
                "confidence": float(pred["confidence"]),
            }
            
            # Add points if requested and available
            if include_points and "points" in pred:
                global_points = [
                    (int(p["x"]), int(p["y"]))
                    for p in pred["points"]
                ]
                segment["points"] = [(int(x), int(y)) for x, y in global_points]
            
            processed_segmentations.append(segment)
        
        logger.info(f"Transformed {len(processed_segmentations)} segments")
        return processed_segmentations
    
    def run_custom_model(
        self, 
        image: np.ndarray, 
        model_id: str
    ) -> Tuple[str, Dict[str, Any], float]:
        """
        Run inference with a custom model ID.
        
        Args:
            image: Input image as numpy array
            model_id: Custom model ID
            
        Returns:
            tuple: (operation_name, result_dict, elapsed_time)
        """
        try:
            start_time = time.time()
            model = self._get_model(model_id)
            
            logger.info(f"Running inference with custom model {model_id}")
            result = model.infer(image)
            elapsed_time = time.time() - start_time
            
            logger.info(f"Custom model inference completed in {elapsed_time:.3f}s")
            
            return "CustomDetection", result[0].dict(), elapsed_time
            
        except RoboflowAPIError:
            raise
        except Exception as e:
            logger.error(f"Custom model inference failed: {str(e)}")
            raise ModelInferenceError(
                f"Failed to run custom model inference: {model_id}",
                details={"model_id": model_id, "error": str(e)}
            )
    
    def clear_model_cache(self):
        """Clear the model cache to free memory."""
        logger.info("Clearing Roboflow model cache")
        self._models.clear()
    
    def get_loaded_models(self) -> list:
        """Get list of currently loaded model IDs."""
        return list(self._models.keys())
