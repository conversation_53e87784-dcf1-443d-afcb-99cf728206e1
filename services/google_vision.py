"""Google Vision service for aibase-ml application."""

import time
import base64
import requests
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import GoogleVisionError

logger = get_logger(__name__)


class GoogleVisionService:
    """Service for handling Google Vision API operations."""
    
    def __init__(self):
        """Initialize Google Vision service."""
        self.settings = get_settings()
        self.api_key = self.settings.google_vision_api_key
        self.base_url = "https://vision.googleapis.com/v1/images:annotate"
    
    def run_ocr_on_image(self, image: np.ndarray) -> str:
        """
        Run OCR on a single image using Google Vision API.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            str: Extracted text from the image
        """
        try:
            # Encode image to base64
            success, encoded_image = cv2.imencode(".jpg", image)
            if not success:
                raise GoogleVisionError("Failed to encode image for <PERSON>CR")
            
            content = base64.b64encode(encoded_image.tobytes()).decode("utf-8")
            
            # Prepare request payload
            request_payload = {
                "requests": [{
                    "image": {"content": content},
                    "features": [{"type": "TEXT_DETECTION"}]
                }]
            }
            
            # Make API request
            url = f"{self.base_url}?key={self.api_key}"
            response = requests.post(
                url, 
                headers={"Content-Type": "application/json"}, 
                json=request_payload,
                timeout=30
            )
            
            if response.status_code != 200:
                raise GoogleVisionError(
                    f"Google Vision API request failed with status {response.status_code}",
                    details={"status_code": response.status_code, "response": response.text}
                )
            
            # Extract text from response
            json_data = response.json()
            text = json_data.get("responses", [{}])[0].get("fullTextAnnotation", {}).get("text", "No text")
            
            return text.strip()
            
        except GoogleVisionError:
            raise
        except Exception as e:
            logger.error(f"Google Vision OCR failed: {str(e)}")
            raise GoogleVisionError(
                "Failed to perform OCR on image",
                details={"error": str(e)}
            )
    
    def run_ocr_on_cropped_images(self, cropped_images: List[np.ndarray]) -> Tuple[str, List[str], float]:
        """
        Run Google OCR on a list of cropped images.
        
        Args:
            cropped_images: List of cropped images as numpy arrays
            
        Returns:
            tuple: (operation_name, results_list, elapsed_time)
        """
        results = []
        start_time = time.time()
        
        logger.info(f"Running Google Vision OCR on {len(cropped_images)} images")
        
        for idx, img in enumerate(cropped_images):
            try:
                text = self.run_ocr_on_image(img)
                results.append(text)
                logger.debug(f"OCR completed for image {idx + 1}/{len(cropped_images)}")
                
            except GoogleVisionError as e:
                logger.warning(f"OCR failed for image {idx}: {e.message}")
                results.append(f"[{idx}] Error: {e.message}")
            except Exception as e:
                logger.warning(f"Unexpected error for image {idx}: {str(e)}")
                results.append(f"[{idx}] Error: {str(e)}")
        
        elapsed_time = time.time() - start_time
        logger.info(f"Google Vision OCR completed in {elapsed_time:.3f}s")
        
        return "Google OCR", results, elapsed_time
    
    def run_ocr_with_detailed_response(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Run OCR on an image and return detailed response including bounding boxes.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            dict: Detailed OCR response with text annotations
        """
        try:
            # Encode image to base64
            success, encoded_image = cv2.imencode(".jpg", image)
            if not success:
                raise GoogleVisionError("Failed to encode image for detailed OCR")
            
            content = base64.b64encode(encoded_image.tobytes()).decode("utf-8")
            
            # Prepare request payload for detailed text detection
            request_payload = {
                "requests": [{
                    "image": {"content": content},
                    "features": [
                        {"type": "TEXT_DETECTION"},
                        {"type": "DOCUMENT_TEXT_DETECTION"}
                    ]
                }]
            }
            
            # Make API request
            url = f"{self.base_url}?key={self.api_key}"
            response = requests.post(
                url, 
                headers={"Content-Type": "application/json"}, 
                json=request_payload,
                timeout=30
            )
            
            if response.status_code != 200:
                raise GoogleVisionError(
                    f"Google Vision API request failed with status {response.status_code}",
                    details={"status_code": response.status_code, "response": response.text}
                )
            
            return response.json()
            
        except GoogleVisionError:
            raise
        except Exception as e:
            logger.error(f"Google Vision detailed OCR failed: {str(e)}")
            raise GoogleVisionError(
                "Failed to perform detailed OCR on image",
                details={"error": str(e)}
            )
    
    def extract_text_blocks(self, detailed_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract text blocks with bounding boxes from detailed OCR response.
        
        Args:
            detailed_response: Detailed response from Google Vision API
            
        Returns:
            list: List of text blocks with coordinates and text
        """
        text_blocks = []
        
        try:
            responses = detailed_response.get("responses", [])
            if not responses:
                return text_blocks
            
            text_annotations = responses[0].get("textAnnotations", [])
            
            for annotation in text_annotations[1:]:  # Skip the first one (full text)
                vertices = annotation.get("boundingPoly", {}).get("vertices", [])
                if len(vertices) >= 4:
                    # Calculate bounding box
                    x_coords = [v.get("x", 0) for v in vertices]
                    y_coords = [v.get("y", 0) for v in vertices]
                    
                    text_block = {
                        "text": annotation.get("description", ""),
                        "x": min(x_coords),
                        "y": min(y_coords),
                        "width": max(x_coords) - min(x_coords),
                        "height": max(y_coords) - min(y_coords),
                        "vertices": vertices
                    }
                    text_blocks.append(text_block)
            
            logger.info(f"Extracted {len(text_blocks)} text blocks")
            return text_blocks
            
        except Exception as e:
            logger.error(f"Failed to extract text blocks: {str(e)}")
            raise GoogleVisionError(
                "Failed to extract text blocks from OCR response",
                details={"error": str(e)}
            )
    
    def validate_api_key(self) -> bool:
        """
        Validate the Google Vision API key by making a test request.
        
        Returns:
            bool: True if API key is valid
        """
        try:
            # Create a small test image
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
            self.run_ocr_on_image(test_image)
            return True
        except GoogleVisionError:
            return False
        except Exception:
            return False
