"""Gemini service for aibase-ml application."""

import time
import cv2
import numpy as np
from typing import List, Tuple, Optional
from google import genai
from google.genai import types

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import GeminiAPIError

logger = get_logger(__name__)


class GeminiService:
    """Service for handling Gemini AI operations."""
    
    def __init__(self):
        """Initialize Gemini service."""
        self.settings = get_settings()
        self.api_key = self.settings.gemini_api_key
        self._client = None
    
    @property
    def client(self):
        """Get or create Gemini client."""
        if self._client is None:
            try:
                self._client = genai.Client(api_key=self.api_key)
            except Exception as e:
                raise GeminiAPIError(
                    "Failed to initialize Gemini client",
                    details={"error": str(e)}
                )
        return self._client
    
    def generate_depth_extraction_prompt(
        self, 
        depth_from: float, 
        depth_to: float
    ) -> str:
        """
        Generate a prompt for depth extraction from images.
        
        Args:
            depth_from: Minimum depth value
            depth_to: Maximum depth value
            
        Returns:
            str: Generated prompt for depth extraction
        """
        return f"""
        Perform OCR on the given image and extract any numeric values that may represent depth. The depth value may appear in various formats,
        such as 'HD: xx.xx', or standalone numbers like 'CIL x.x' and 'CIL x.x' but priority is HD: xx.xx or sometime you can detect as HO, H0, HB,...

        Some values may be incorrectly formatted, such as '2-70', '2/70', or '2*70'. Convert these into the correct format as '2.70' before performing the comparison.

        ### **Rules for Extraction:**
        - Number must be falls **within the range of {depth_from} to {depth_to}**, return it as the most probable depth.
        - Image was sort with ascending with the depth, so the result should increase in each crop
        - If **multiple depth candidates** exist, prioritize the value that follows a common depth-related keyword (e.g., **HD, D, R, CIL**).
        - If a number appears alone (e.g., **CIL 0.9**), but no clear depth label exists, **still consider it if it matches the range**.
        - If a depth value is found **with surrounding words**, extract only the number (e.g., from 'HD: 120.6', return only `120.6`).
        - If no valid depth values exist and the text contains **only words**, return `'Not found'`.

        ### **Output Rules:**
        - Return **only** the depth value as a number (e.g., `5.5`, `10.43`, `1.2`).
        - Ensure **no extra characters** like `5.5\\n`, `1.2\\n`, or `120.6 HD`—return only the number.
        - Trim whitespace for the final result
        - Do not exaplain any thing, just numbers
        """
    
    def run_prompt_on_image(
        self, 
        image: np.ndarray, 
        prompt: str,
        model: str = 'gemini-2.0-flash-lite'
    ) -> str:
        """
        Run a prompt on a single image using Gemini.
        
        Args:
            image: Input image as numpy array
            prompt: Text prompt for the model
            model: Gemini model to use
            
        Returns:
            str: Generated response from Gemini
        """
        try:
            # Encode image to bytes
            success, encoded_image = cv2.imencode(".jpg", image)
            if not success:
                raise GeminiAPIError("Failed to encode image for Gemini processing")
            
            image_bytes = encoded_image.tobytes()
            
            # Generate content with Gemini
            response = self.client.models.generate_content(
                model=model,
                contents=[
                    types.Part.from_bytes(data=image_bytes, mime_type='image/jpeg'),
                    prompt
                ]
            )
            
            return response.text.strip()
            
        except GeminiAPIError:
            raise
        except Exception as e:
            logger.error(f"Gemini processing failed: {str(e)}")
            raise GeminiAPIError(
                "Failed to process image with Gemini",
                details={"error": str(e), "model": model}
            )
    
    def run_prompt_on_cropped_images(
        self, 
        cropped_images: List[np.ndarray], 
        prompt: str,
        model: str = 'gemini-2.0-flash-lite'
    ) -> Tuple[str, List[str], float]:
        """
        Run Gemini model on a list of cropped images with the same prompt.
        
        Args:
            cropped_images: List of cropped images as numpy arrays
            prompt: Text prompt for the model
            model: Gemini model to use
            
        Returns:
            tuple: (operation_name, responses_list, elapsed_time)
        """
        responses = []
        start_time = time.time()
        
        logger.info(f"Running Gemini processing on {len(cropped_images)} images")
        
        for idx, img in enumerate(cropped_images):
            try:
                response = self.run_prompt_on_image(img, prompt, model)
                responses.append(response)
                logger.debug(f"Gemini processing completed for image {idx + 1}/{len(cropped_images)}")
                
            except GeminiAPIError as e:
                logger.warning(f"Gemini processing failed for image {idx}: {e.message}")
                responses.append(f"[{idx}] Error: {e.message}")
            except Exception as e:
                logger.warning(f"Unexpected error for image {idx}: {str(e)}")
                responses.append(f"[{idx}] Error: {str(e)}")
        
        elapsed_time = time.time() - start_time
        logger.info(f"Gemini processing completed in {elapsed_time:.3f}s")
        
        return "Gemini", responses, elapsed_time
    
    def run_depth_extraction(
        self, 
        cropped_images: List[np.ndarray],
        depth_from: float,
        depth_to: float,
        custom_prompt: Optional[str] = None
    ) -> Tuple[str, List[str], float]:
        """
        Run depth extraction on cropped images.
        
        Args:
            cropped_images: List of cropped images as numpy arrays
            depth_from: Minimum depth value
            depth_to: Maximum depth value
            custom_prompt: Optional custom prompt, uses default if None
            
        Returns:
            tuple: (operation_name, responses_list, elapsed_time)
        """
        if custom_prompt is None:
            prompt = self.generate_depth_extraction_prompt(depth_from, depth_to)
        else:
            prompt = custom_prompt
        
        return self.run_prompt_on_cropped_images(cropped_images, prompt)
    
    def run_general_ocr(
        self, 
        cropped_images: List[np.ndarray]
    ) -> Tuple[str, List[str], float]:
        """
        Run general OCR on cropped images.
        
        Args:
            cropped_images: List of cropped images as numpy arrays
            
        Returns:
            tuple: (operation_name, responses_list, elapsed_time)
        """
        prompt = """
        Perform OCR on the given image and extract all visible text.
        Return only the extracted text without any additional formatting or explanation.
        If no text is found, return 'No text found'.
        """
        
        return self.run_prompt_on_cropped_images(cropped_images, prompt)
    
    def validate_api_key(self) -> bool:
        """
        Validate the Gemini API key by making a test request.
        
        Returns:
            bool: True if API key is valid
        """
        try:
            # Create a small test image with text
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "TEST", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            self.run_prompt_on_image(test_image, "What text do you see in this image?")
            return True
        except GeminiAPIError:
            return False
        except Exception:
            return False
