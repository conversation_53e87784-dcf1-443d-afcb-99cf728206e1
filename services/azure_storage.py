"""Azure Storage service for aibase-ml application."""

import cv2
import uuid
import numpy as np
from datetime import datetime
from typing import Optional, Dict, Any
from azure.storage.blob import BlobServiceClient

from config.settings import get_settings
from config.logging import get_logger
from core.exceptions import AzureStorageError

logger = get_logger(__name__)


class AzureStorageService:
    """Service for handling Azure Blob Storage operations."""
    
    def __init__(self):
        """Initialize Azure Storage service."""
        self.settings = get_settings()
        self._blob_service_client = None
    
    @property
    def blob_service_client(self) -> BlobServiceClient:
        """Get or create blob service client."""
        if self._blob_service_client is None:
            try:
                self._blob_service_client = BlobServiceClient.from_connection_string(
                    self.settings.azure_storage_connection_string
                )
            except Exception as e:
                raise AzureStorageError(
                    "Failed to initialize Azure Storage client",
                    details={"error": str(e)}
                )
        return self._blob_service_client
    
    def upload_image(
        self, 
        image_array: np.ndarray, 
        filename: Optional[str] = None,
        container_path: Optional[str] = None,
        content_type: str = 'image/jpeg'
    ) -> Dict[str, Any]:
        """
        Upload an image array to Azure Blob Storage.
        
        Args:
            image_array: numpy array representing the image
            filename: optional filename, if not provided, generates a unique one
            container_path: optional container path, defaults to warped_image path
            content_type: MIME type of the image
            
        Returns:
            dict: Contains 'success', 'url', 'filename', and 'container_path' keys
        """
        try:
            # Generate filename if not provided
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = str(uuid.uuid4())[:8]
                filename = f"warped_image_{timestamp}_{unique_id}.jpg"
            
            # Use default container path if not provided
            if container_path is None:
                container_path = self.settings.azure_warped_container_path
            
            # Convert image array to bytes
            success, buffer = cv2.imencode('.jpg', image_array)
            if not success:
                raise AzureStorageError("Failed to encode image to JPEG format")
            
            image_bytes = buffer.tobytes()
            
            # Get blob client
            blob_client = self.blob_service_client.get_blob_client(
                container=container_path,
                blob=filename
            )
            
            # Upload the image
            blob_client.upload_blob(
                image_bytes, 
                overwrite=True, 
                content_type=content_type
            )
            
            # Generate the URL
            blob_url = f"https://aibaseimagestorage.blob.core.windows.net/{container_path}/{filename}"
            
            logger.info(f"Successfully uploaded image to Azure Storage: {filename}")
            
            return {
                "success": True,
                "url": blob_url,
                "filename": filename,
                "container_path": container_path,
                "size_bytes": len(image_bytes)
            }
            
        except AzureStorageError:
            raise
        except Exception as e:
            logger.error(f"Failed to upload image to Azure Storage: {str(e)}")
            raise AzureStorageError(
                "Failed to upload image to Azure Storage",
                details={"error": str(e), "filename": filename}
            )
    
    def upload_warped_image(
        self, 
        image_array: np.ndarray, 
        original_filename: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upload a warped image with standardized naming.
        
        Args:
            image_array: numpy array representing the warped image
            original_filename: original filename to base the warped filename on
            
        Returns:
            dict: Upload result with URL and filename
        """
        # Create warped filename
        if original_filename:
            # Remove the original extension and add _warped.jpg
            name_without_ext = (
                original_filename.rsplit('.', 1)[0] 
                if '.' in original_filename 
                else original_filename
            )
            warped_filename = f"{name_without_ext}_warped.jpg"
        else:
            warped_filename = None
        
        return self.upload_image(
            image_array=image_array,
            filename=warped_filename,
            container_path=self.settings.azure_warped_container_path
        )
    
    def delete_blob(self, filename: str, container_path: Optional[str] = None) -> bool:
        """
        Delete a blob from Azure Storage.
        
        Args:
            filename: Name of the file to delete
            container_path: Container path, defaults to warped_image path
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            if container_path is None:
                container_path = self.settings.azure_warped_container_path
            
            blob_client = self.blob_service_client.get_blob_client(
                container=container_path,
                blob=filename
            )
            
            blob_client.delete_blob()
            logger.info(f"Successfully deleted blob: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete blob {filename}: {str(e)}")
            raise AzureStorageError(
                "Failed to delete blob",
                details={"error": str(e), "filename": filename}
            )
    
    def blob_exists(self, filename: str, container_path: Optional[str] = None) -> bool:
        """
        Check if a blob exists in Azure Storage.
        
        Args:
            filename: Name of the file to check
            container_path: Container path, defaults to warped_image path
            
        Returns:
            bool: True if blob exists
        """
        try:
            if container_path is None:
                container_path = self.settings.azure_warped_container_path
            
            blob_client = self.blob_service_client.get_blob_client(
                container=container_path,
                blob=filename
            )
            
            return blob_client.exists()
            
        except Exception as e:
            logger.error(f"Failed to check blob existence {filename}: {str(e)}")
            return False
