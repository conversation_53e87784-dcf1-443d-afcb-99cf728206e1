[tool:pytest]
# Pytest configuration for aibase-ml

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    external: Tests requiring external services
    e2e: End-to-end tests
    api: API endpoint tests
    service: Service layer tests
    processor: Processor tests
    config: Configuration tests
    slow: Slow running tests

# Output options
addopts = 
    --strict-markers
    --tb=short
    --disable-warnings
    -ra

# Coverage options
[coverage:run]
source = .
omit = 
    tests/*
    venv/*
    env/*
    .venv/*
    */site-packages/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
