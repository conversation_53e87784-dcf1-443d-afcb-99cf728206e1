# API Flow Charts - aibase-ml

This document contains detailed flow charts for all 4 APIs in the refactored aibase-ml application.

## Architecture Overview

The aibase-ml application follows a layered architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "API Layer"
        A1[Main Processing<br/>Port 8386]
        A2[Segment Auto Crop<br/>Port 8388]
        A3[Segment Core Pieces<br/>Port 8381]
        A4[Segment Core Server<br/>Port 8389]
    end

    subgraph "Core Layer"
        C1[Error Handling]
        C2[Response Formatting]
        C3[Validation Decorators]
        C4[Logging Config]
    end

    subgraph "Service Layer"
        S1[Roboflow Service]
        S2[Google Vision Service]
        S3[Gemini Service]
        S4[Azure Storage Service]
        S5[Image Processing Service]
    end

    subgraph "Processor Layer"
        P1[Transformation Processor]
    end

    subgraph "Configuration"
        CF1[Settings Management]
        CF2[Environment Variables]
    end

    subgraph "External Services"
        E1[Roboflow API]
        E2[Google Vision API]
        E3[Gemini AI API]
        E4[Azure Blob Storage]
    end

    A1 --> C1
    A2 --> C2
    A3 --> C3
    A4 --> C4

    C1 --> S1
    C2 --> S2
    C3 --> S3
    C4 --> S4
    C1 --> S5

    S1 --> P1
    S5 --> P1

    S1 --> E1
    S2 --> E2
    S3 --> E3
    S4 --> E4

    CF1 --> S1
    CF1 --> S2
    CF1 --> S3
    CF1 --> S4

    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style A3 fill:#e3f2fd
    style A4 fill:#e3f2fd
    style S1 fill:#f1f8e9
    style S2 fill:#f1f8e9
    style S3 fill:#f1f8e9
    style S4 fill:#f1f8e9
    style S5 fill:#f1f8e9
    style E1 fill:#fff3e0
    style E2 fill:#fff3e0
    style E3 fill:#fff3e0
    style E4 fill:#fff3e0
```

**Architecture Layers:**
- **API Layer**: 4 Flask endpoints handling different processing workflows
- **Core Layer**: Error handling, response formatting, and validation decorators
- **Service Layer**: Reusable business logic services for external integrations
- **Processor Layer**: Specialized processing logic for transformations
- **Configuration**: Centralized settings and logging management

## API Endpoints Overview

| API | Port | Purpose | Key Features |
|-----|------|---------|--------------|
| Main Processing | 8386 | Complete processing pipeline | OCR + AI text processing + segmentation |
| Segment Auto Crop | 8388 | Segmentation with auto cropping | Perspective transformation + alignment |
| Segment Core Pieces | 8381 | Core piece extraction | Segmentation + transformation |
| Segment Core Server | 8389 | Block processing with OCR/AI | Most comprehensive processing |

## Flow Chart Details

### 1. Main Processing API (`/process`)

**Purpose**: Complete image processing pipeline with optional segmentation, OCR, and AI text processing.

```mermaid
flowchart TD
    A[POST /process] --> B[Validate File Upload]
    B --> C[Decode Image]
    C --> D[Roboflow Segmentation]
    D --> E[Transform Segment Format]
    E --> F{use_segmentation?}

    F -->|true| G[Apply Perspective Transform]
    F -->|false| H[Skip Transform]

    G --> I[Upload to Azure Storage]
    H --> I

    I --> J[Extract Block Segments]
    J --> K[Google Vision OCR]
    K --> L{custom_prompt?}

    L -->|yes| M[Gemini Custom Processing]
    L -->|no| N[Gemini Depth Extraction]

    M --> O[Combine Results]
    N --> O

    O --> P[Format Response]
    P --> Q[Return JSON Response]

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style F fill:#fff3e0
    style L fill:#fff3e0
```

**Key Steps**:
1. File validation and image decoding
2. Roboflow segmentation and format transformation
3. Optional perspective transformation based on `use_segmentation` flag
4. Azure upload for warped images
5. Google Vision OCR on block segments
6. Gemini AI processing (custom prompt or depth extraction)
7. Combined response with all results

**Response Format**:
```json
{
  "warped_image": "base64_encoded_image",
  "google_result": ["ocr_text_1", "ocr_text_2"],
  "gemini_result": ["ai_result_1", "ai_result_2"],
  "x": [x_coordinates],
  "y": [y_coordinates],
  "width": [widths],
  "height": [heights]
}
```

### 2. Segment Auto Crop API (`/segment_crop`)

**Purpose**: Segmentation with automatic cropping and perspective transformation.

```mermaid
flowchart TD
    A[POST /segment_crop] --> B[Validate File Upload]
    B --> C[Decode Image]
    C --> D[Roboflow Box-Core-Block Segmentation]
    D --> E[Merge Overlapping Boxes]
    E --> F[Filter by Confidence]
    F --> G[Apply Perspective Transform]
    G --> H[Upload Warped Image to Azure]
    H --> I[Align Segments to Transformed Image]
    I --> J[Convert Core Segments to Row Format]
    J --> K[Format Detection Response]
    K --> L[Return JSON with Azure URLs]

    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style G fill:#ffecb3
    style H fill:#f3e5f5
```

**Key Steps**:
1. File validation and image decoding
2. Roboflow box-core-block segmentation
3. Box merging and segment filtering
4. Mandatory perspective transformation
5. Azure upload for warped image
6. Segment alignment and core-to-row conversion
7. Response with detection results and Azure URLs

**Response Format**:
```json
{
  "detection": {
    "predictions": [segment_objects]
  },
  "warped_image_url": "azure_blob_url",
  "warped_image_filename": "filename.jpg"
}
```

### 3. Segment Core Pieces API (`/segment_core_pieces`)

**Purpose**: Core segmentation with perspective transformation and piece extraction.

```mermaid
flowchart TD
    A[POST /segment_core_pieces] --> B[Validate File Upload]
    B --> C[Decode Image]
    C --> D[Roboflow Segmentation with Points]
    D --> E[Merge Overlapping Boxes]
    E --> F[Filter by Confidence]
    F --> G[Apply Perspective Transform]
    G --> H[Upload Warped Image to Azure]
    H --> I[Align Segments to Transformed Image]
    I --> J[Convert Core Segments to Row Format]
    J --> K[Format Segmentation Response]
    K --> L[Return JSON with Segmentation Key]

    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style G fill:#ffecb3
    style K fill:#e8f5e8
```

**Key Steps**:
1. File validation and image decoding
2. Roboflow segmentation with points
3. Box merging and filtering
4. Perspective transformation
5. Azure upload
6. Segment alignment and core conversion
7. Response with segmentation key (different from detection)

**Response Format**:
```json
{
  "segmentation": {
    "predictions": [segment_objects]
  },
  "warped_image_url": "azure_blob_url"
}
```

### 4. Segment Core Server API (`/process_core_outline`)

**Purpose**: Most comprehensive API with Google OCR and Gemini processing on block segments.

```mermaid
flowchart TD
    A[POST /process_core_outline] --> B[Validate File Upload]
    B --> C[Parse Parameters]
    C --> D[Decode Image]
    D --> E[Roboflow Segmentation]
    E --> F{use_row?}

    F -->|true| G[Apply Perspective Transform]
    F -->|false| H[Skip Transform]

    G --> I[Extract Block Segments]
    H --> I

    I --> J[Crop Block Images]
    J --> K[Google Vision OCR on Each Block]
    K --> L{custom_prompt?}

    L -->|yes| M[Gemini Custom Processing]
    L -->|no| N[Gemini Depth Extraction]

    M --> O[Combine OCR + AI Results per Block]
    N --> O

    O --> P[Upload Warped Image to Azure]
    P --> Q[Format Block Response]
    Q --> R[Return JSON with Block Details]

    style A fill:#e1f5fe
    style R fill:#c8e6c9
    style F fill:#fff3e0
    style L fill:#fff3e0
    style K fill:#e3f2fd
    style M fill:#f1f8e9
    style N fill:#f1f8e9
```

**Key Steps**:
1. File validation and parameter parsing
2. Roboflow segmentation
3. Optional transformation based on `use_row` parameter
4. Block segment extraction and cropping
5. Google Vision OCR on all blocks
6. Gemini processing (custom prompt or depth extraction)
7. Combined results with google_text and gemini_text per block

**Parameters**:
- `use_row`: Boolean for transformation (default: true)
- `depth_from`: Minimum depth value (default: 0)
- `depth_to`: Maximum depth value (default: 2.4)
- `custom_prompt`: Optional custom prompt for Gemini

**Response Format**:
```json
{
  "blocks": [
    {
      "class": "Block",
      "x": 100, "y": 100, "width": 50, "height": 50,
      "confidence": 0.8,
      "google_text": "OCR extracted text",
      "gemini_text": "AI processed result"
    }
  ],
  "warped_image_url": "azure_blob_url"
}
```

## Common Processing Steps

### Roboflow Segmentation
All APIs use the same Roboflow box-core-block segmentation model with confidence filtering and format transformation.

### Perspective Transformation
Applied when:
- Main Processing: `use_segmentation=true`
- Segment Auto Crop: Always applied
- Segment Core Pieces: Always applied
- Segment Core Server: `use_row=true` (default)

### Azure Storage
Warped images are uploaded to Azure Blob Storage with generated filenames and returned URLs.

### Error Handling
All APIs use consistent error handling with:
- 400 errors for invalid input
- 500 errors for processing failures
- Detailed error messages and logging

## Service Interaction Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Roboflow
    participant ImageProc as Image Processing
    participant Transform as Transformation
    participant Azure
    participant GoogleVision as Google Vision
    participant Gemini

    Client->>API: POST /process (image file)
    API->>API: Validate & decode image

    API->>Roboflow: Segment image
    Roboflow-->>API: Segmentation results

    API->>ImageProc: Process segments
    ImageProc-->>API: Processed segments

    opt Transformation Required
        API->>Transform: Apply perspective transform
        Transform-->>API: Warped image
    end

    opt Azure Upload
        API->>Azure: Upload warped image
        Azure-->>API: Blob URL
    end

    opt OCR Processing
        API->>GoogleVision: Extract text from blocks
        GoogleVision-->>API: OCR results
    end

    opt AI Processing
        API->>Gemini: Process text/depth
        Gemini-->>API: AI analysis
    end

    API->>API: Combine all results
    API-->>Client: JSON response
```

## Data Flow Diagram

```mermaid
flowchart LR
    subgraph Input
        I1[Original Image]
        I2[Parameters]
    end

    subgraph Processing
        P1[Roboflow<br/>Segmentation]
        P2[Image<br/>Processing]
        P3[Perspective<br/>Transform]
        P4[Block<br/>Extraction]
        P5[OCR<br/>Processing]
        P6[AI<br/>Analysis]
    end

    subgraph Output
        O1[Warped Image]
        O2[Segment Data]
        O3[OCR Text]
        O4[AI Results]
        O5[Azure URLs]
    end

    subgraph Storage
        S1[Azure Blob<br/>Storage]
    end

    I1 --> P1
    I2 --> P1
    P1 --> P2
    P2 --> P3
    P3 --> P4
    P4 --> P5
    P5 --> P6

    P3 --> O1
    P2 --> O2
    P5 --> O3
    P6 --> O4

    O1 --> S1
    S1 --> O5

    style I1 fill:#e3f2fd
    style I2 fill:#e3f2fd
    style O1 fill:#c8e6c9
    style O2 fill:#c8e6c9
    style O3 fill:#c8e6c9
    style O4 fill:#c8e6c9
    style O5 fill:#c8e6c9
    style S1 fill:#fff3e0
```

## Service Dependencies

### Required Services
- **RoboflowService**: ML model inference
- **ImageProcessingService**: Image operations and segment processing
- **TransformationProcessor**: Perspective transformations
- **AzureStorageService**: Cloud storage (optional, continues on failure)

### Optional Services (API-specific)
- **GoogleVisionService**: OCR (Main Processing, Segment Core Server)
- **GeminiService**: AI text processing (Main Processing, Segment Core Server)

## Performance Considerations

### Processing Times
- Roboflow inference: ~1-3 seconds
- Perspective transformation: ~0.1-0.5 seconds
- Google Vision OCR: ~0.5-2 seconds per block
- Gemini processing: ~1-3 seconds per block
- Azure upload: ~0.5-2 seconds

### Memory Usage
- Original image: Variable based on input
- Warped image: Similar to original
- Cropped blocks: Small segments, minimal memory impact
- Total peak memory: ~2-3x original image size

### Optimization Tips
- Use appropriate image sizes (recommended: 1000-2000px max dimension)
- Consider batch processing for multiple images
- Monitor Azure storage costs for warped image uploads
- Cache Roboflow models for better performance
