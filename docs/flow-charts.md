# API Flow Charts - aibase-ml

This document contains detailed flow charts for all 4 APIs in the refactored aibase-ml application.

## Architecture Overview

The aibase-ml application follows a layered architecture with clear separation of concerns:

- **API Layer**: 4 Flask endpoints handling different processing workflows
- **Core Layer**: Error handling, response formatting, and validation decorators
- **Service Layer**: Reusable business logic services for external integrations
- **Processor Layer**: Specialized processing logic for transformations
- **Configuration**: Centralized settings and logging management

## API Endpoints Overview

| API | Port | Purpose | Key Features |
|-----|------|---------|--------------|
| Main Processing | 8386 | Complete processing pipeline | OCR + AI text processing + segmentation |
| Segment Auto Crop | 8388 | Segmentation with auto cropping | Perspective transformation + alignment |
| Segment Core Pieces | 8381 | Core piece extraction | Segmentation + transformation |
| Segment Core Server | 8389 | Block processing with OCR/AI | Most comprehensive processing |

## Flow Chart Details

### 1. Main Processing API (`/process`)

**Purpose**: Complete image processing pipeline with optional segmentation, OCR, and AI text processing.

**Key Steps**:
1. File validation and image decoding
2. Roboflow segmentation and format transformation
3. Optional perspective transformation based on `use_segmentation` flag
4. Azure upload for warped images
5. Google Vision OCR on block segments
6. Gemini AI processing (custom prompt or depth extraction)
7. Combined response with all results

**Response Format**:
```json
{
  "warped_image": "base64_encoded_image",
  "google_result": ["ocr_text_1", "ocr_text_2"],
  "gemini_result": ["ai_result_1", "ai_result_2"],
  "x": [x_coordinates],
  "y": [y_coordinates],
  "width": [widths],
  "height": [heights]
}
```

### 2. Segment Auto Crop API (`/segment_crop`)

**Purpose**: Segmentation with automatic cropping and perspective transformation.

**Key Steps**:
1. File validation and image decoding
2. Roboflow box-core-block segmentation
3. Box merging and segment filtering
4. Mandatory perspective transformation
5. Azure upload for warped image
6. Segment alignment and core-to-row conversion
7. Response with detection results and Azure URLs

**Response Format**:
```json
{
  "detection": {
    "predictions": [segment_objects]
  },
  "warped_image_url": "azure_blob_url",
  "warped_image_filename": "filename.jpg"
}
```

### 3. Segment Core Pieces API (`/segment_core_pieces`)

**Purpose**: Core segmentation with perspective transformation and piece extraction.

**Key Steps**:
1. File validation and image decoding
2. Roboflow segmentation with points
3. Box merging and filtering
4. Perspective transformation
5. Azure upload
6. Segment alignment and core conversion
7. Response with segmentation key (different from detection)

**Response Format**:
```json
{
  "segmentation": {
    "predictions": [segment_objects]
  },
  "warped_image_url": "azure_blob_url"
}
```

### 4. Segment Core Server API (`/process_core_outline`)

**Purpose**: Most comprehensive API with Google OCR and Gemini processing on block segments.

**Key Steps**:
1. File validation and parameter parsing
2. Roboflow segmentation
3. Optional transformation based on `use_row` parameter
4. Block segment extraction and cropping
5. Google Vision OCR on all blocks
6. Gemini processing (custom prompt or depth extraction)
7. Combined results with google_text and gemini_text per block

**Parameters**:
- `use_row`: Boolean for transformation (default: true)
- `depth_from`: Minimum depth value (default: 0)
- `depth_to`: Maximum depth value (default: 2.4)
- `custom_prompt`: Optional custom prompt for Gemini

**Response Format**:
```json
{
  "blocks": [
    {
      "class": "Block",
      "x": 100, "y": 100, "width": 50, "height": 50,
      "confidence": 0.8,
      "google_text": "OCR extracted text",
      "gemini_text": "AI processed result"
    }
  ],
  "warped_image_url": "azure_blob_url"
}
```

## Common Processing Steps

### Roboflow Segmentation
All APIs use the same Roboflow box-core-block segmentation model with confidence filtering and format transformation.

### Perspective Transformation
Applied when:
- Main Processing: `use_segmentation=true`
- Segment Auto Crop: Always applied
- Segment Core Pieces: Always applied  
- Segment Core Server: `use_row=true` (default)

### Azure Storage
Warped images are uploaded to Azure Blob Storage with generated filenames and returned URLs.

### Error Handling
All APIs use consistent error handling with:
- 400 errors for invalid input
- 500 errors for processing failures
- Detailed error messages and logging

## Service Dependencies

### Required Services
- **RoboflowService**: ML model inference
- **ImageProcessingService**: Image operations and segment processing
- **TransformationProcessor**: Perspective transformations
- **AzureStorageService**: Cloud storage (optional, continues on failure)

### Optional Services (API-specific)
- **GoogleVisionService**: OCR (Main Processing, Segment Core Server)
- **GeminiService**: AI text processing (Main Processing, Segment Core Server)

## Performance Considerations

### Processing Times
- Roboflow inference: ~1-3 seconds
- Perspective transformation: ~0.1-0.5 seconds
- Google Vision OCR: ~0.5-2 seconds per block
- Gemini processing: ~1-3 seconds per block
- Azure upload: ~0.5-2 seconds

### Memory Usage
- Original image: Variable based on input
- Warped image: Similar to original
- Cropped blocks: Small segments, minimal memory impact
- Total peak memory: ~2-3x original image size

### Optimization Tips
- Use appropriate image sizes (recommended: 1000-2000px max dimension)
- Consider batch processing for multiple images
- Monitor Azure storage costs for warped image uploads
- Cache Roboflow models for better performance
