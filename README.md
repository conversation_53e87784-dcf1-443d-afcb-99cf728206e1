# aibase-ml

A comprehensive machine learning API system for geological core sample analysis, featuring automated segmentation, image processing, OCR text extraction, and AI-powered analysis.

## 🌟 Features

- **4 Specialized APIs** for different geological analysis workflows
- **Automated Segmentation** using Roboflow ML models
- **Image Processing** with perspective transformation and alignment
- **OCR Text Extraction** via Google Vision API
- **AI Analysis** using Gemini AI for geological insights
- **Azure Storage Integration** for image hosting and management
- **Comprehensive Testing** with unit, integration, and performance tests
- **Real-time Monitoring** with performance tracking and alerting
- **Clean Architecture** with separation of concerns and dependency injection

## 🏗️ Architecture

```
aibase-ml/
├── apis/                    # API endpoints (Flask applications)
├── services/               # Business logic and external integrations
├── processors/             # Data transformation and processing
├── config/                 # Configuration management
├── core/                   # Core utilities and shared components
├── tests/                  # Comprehensive testing suite
├── testing/                # API testing tools and visualization
├── docs/                   # Documentation and flow charts
└── utils/                  # Legacy utilities (preserved)
```

### Core Components

- **APIs**: 4 Flask-based REST APIs for different analysis workflows
- **Services**: Modular services for external integrations (Roboflow, Google Vision, Gemini AI, Azure Storage)
- **Processors**: Image processing and data transformation pipelines
- **Configuration**: Centralized settings with environment variable support
- **Testing**: Unit tests, integration tests, and performance benchmarks

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Required API keys (see Configuration section)
- 4GB+ RAM recommended
- Multi-core CPU for optimal performance

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd aibase-ml
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment variables:**
```bash
# Copy example configuration
cp .env.example .env

# Edit .env with your API keys
export ROBOFLOW_API_KEY="your_roboflow_api_key"
export GOOGLE_VISION_API_KEY="your_google_vision_api_key"
export GEMINI_API_KEY="your_gemini_api_key"
export AZURE_STORAGE_CONNECTION_STRING="your_azure_connection_string"
```

4. **Start the APIs:**
```bash
# Terminal 1 - Main Processing API (Port 8386)
cd apis && python main_processing.py

# Terminal 2 - Segment Auto Crop API (Port 8388)
cd apis && python segment_auto_crop.py

# Terminal 3 - Segment Core Pieces API (Port 8381)
cd apis && python segment_core_pieces.py

# Terminal 4 - Segment Core Server API (Port 8389)
cd apis && python segment_core_server.py
```

5. **Test the APIs:**
```bash
# Health check
curl http://localhost:8386/health

# Test with sample image
curl -X POST -F "file=@sample_image.jpg" http://localhost:8386/process
```

## 📡 API Endpoints

### 1. Main Processing API (Port 8386)
**Endpoint:** `/process`
**Purpose:** Complete geological analysis pipeline with OCR and AI processing

```bash
curl -X POST \
  -F "file=@core_sample.jpg" \
  -F "use_segmentation=true" \
  -F "depth_from=0.0" \
  -F "depth_to=3.0" \
  -F "custom_prompt=Analyze this geological core sample" \
  http://localhost:8386/process
```

**Response:**
```json
{
  "warped_image": "base64_encoded_image",
  "google_result": ["OCR text results"],
  "gemini_result": ["AI analysis results"],
  "x": [100, 200], "y": [50, 150],
  "width": [300, 300], "height": [100, 100]
}
```

### 2. Segment Auto Crop API (Port 8388)
**Endpoint:** `/segment_crop`
**Purpose:** Automated segmentation with perspective transformation

```bash
curl -X POST -F "file=@core_sample.jpg" http://localhost:8388/segment_crop
```

### 3. Segment Core Pieces API (Port 8381)
**Endpoint:** `/segment_core_pieces`
**Purpose:** Core piece extraction and Row format conversion

```bash
curl -X POST -F "file=@core_sample.jpg" http://localhost:8381/segment_core_pieces
```

### 4. Segment Core Server API (Port 8389)
**Endpoint:** `/process_core_outline`
**Purpose:** Comprehensive block processing with text analysis

```bash
curl -X POST \
  -F "file=@core_sample.jpg" \
  -F "use_row=true" \
  -F "depth_from=0.0" \
  -F "depth_to=3.0" \
  http://localhost:8389/process_core_outline
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Roboflow API Configuration
ROBOFLOW_API_KEY=your_roboflow_api_key
ROBOFLOW_MODEL_VERSION=1

# Google Vision API
GOOGLE_VISION_API_KEY=your_google_vision_api_key

# Gemini AI API
GEMINI_API_KEY=your_gemini_api_key

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string
AZURE_CONTAINER_NAME=aibase-images

# Processing Configuration
MAX_IMAGE_SIZE=2048
CONFIDENCE_THRESHOLD=0.5
PERSPECTIVE_TRANSFORM_ENABLED=true

# API Configuration
MAIN_PROCESSING_PORT=8386
SEGMENT_AUTO_CROP_PORT=8388
SEGMENT_CORE_PIECES_PORT=8381
SEGMENT_CORE_SERVER_PORT=8389
```

### API Keys Setup

1. **Roboflow API Key:**
   - Sign up at [Roboflow](https://roboflow.com)
   - Create a project and get your API key
   - Note your model version

2. **Google Vision API Key:**
   - Enable Google Vision API in Google Cloud Console
   - Create credentials and download API key

3. **Gemini AI API Key:**
   - Get API key from Google AI Studio
   - Enable Gemini API access

4. **Azure Storage:**
   - Create Azure Storage account
   - Get connection string from Azure portal
   - Create a container for image storage

## 🧪 Testing

### Unit Tests
```bash
# Run all unit tests
pytest tests/unit/ -v

# Run specific test modules
pytest tests/unit/test_services.py -v
pytest tests/unit/test_apis.py -v

# Run with coverage
pytest tests/unit/ --cov=services --cov=apis --cov-report=html
```

### Integration Tests
```bash
# Run integration tests (requires running APIs)
python tests/run_integration_tests.py

# Run specific test types
python tests/run_integration_tests.py --type api
python tests/run_integration_tests.py --type external --include-external
python tests/run_integration_tests.py --type e2e
```

### Performance Tests
```bash
# Run performance benchmarks
python tests/performance/run_performance_tests.py

# Run specific performance tests
python tests/performance/run_performance_tests.py --test-type benchmark
python tests/performance/run_performance_tests.py --test-type load
python tests/performance/run_performance_tests.py --test-type stress
```

### API Testing with Visualization
```bash
# Test individual APIs with visualization
python testing/test_runners/single_api_test.py --api main_processing --image sample.jpg

# Test all APIs with comparison
python testing/test_runners/all_apis_test.py --image sample.jpg
```

## 📊 Monitoring and Performance

### Real-time Monitoring
```python
from tests.performance import PerformanceMonitor

monitor = PerformanceMonitor()
monitor.start_monitoring(interval=5.0)
# APIs running...
monitor.stop_monitoring()
monitor.create_performance_dashboard()
```

### Performance Benchmarks
- **Response Times**: < 30s for segment APIs, < 60s for processing APIs
- **Throughput**: > 1.0 requests/second per API
- **Success Rate**: > 95% under normal load
- **Memory Usage**: < 1GB per API instance
- **Concurrent Users**: Support for 3+ concurrent users per API

### Health Monitoring
All APIs provide health endpoints:
```bash
curl http://localhost:8386/health  # Main Processing
curl http://localhost:8388/health  # Segment Auto Crop
curl http://localhost:8381/health  # Segment Core Pieces
curl http://localhost:8389/health  # Segment Core Server
```

## 📁 Project Structure

```
aibase-ml/
├── README.md                       # This file
├── requirements.txt                # Python dependencies
├── pytest.ini                     # Pytest configuration
├── .env.example                    # Environment variables template
│
├── apis/                           # Flask API endpoints
│   ├── main_processing.py          # Complete processing pipeline
│   ├── segment_auto_crop.py        # Segmentation with auto-crop
│   ├── segment_core_pieces.py      # Core piece extraction
│   └── segment_core_server.py      # Comprehensive block processing
│
├── services/                       # Business logic services
│   ├── roboflow_service.py         # Roboflow ML model integration
│   ├── google_vision.py            # Google Vision OCR service
│   ├── gemini_service.py           # Gemini AI text processing
│   ├── azure_storage.py            # Azure Storage operations
│   └── image_processing.py         # Image processing utilities
│
├── processors/                     # Data transformation
│   └── transformation.py           # Perspective transformation
│
├── config/                         # Configuration management
│   └── settings.py                 # Centralized configuration
│
├── core/                          # Core utilities
│   ├── error_handling.py          # Error handling framework
│   └── logging_config.py          # Logging configuration
│
├── tests/                         # Testing suite
│   ├── unit/                      # Unit tests
│   ├── integration/               # Integration tests
│   ├── performance/               # Performance benchmarks
│   └── run_integration_tests.py   # Integration test runner
│
├── testing/                       # API testing tools
│   ├── api_clients/               # API client libraries
│   ├── visualization/             # Visualization utilities
│   ├── test_runners/              # Test execution scripts
│   └── sample_images/             # Test image generation
│
├── docs/                          # Documentation
│   └── flow-charts.md             # API flow charts
│
└── utils/                         # Legacy utilities (preserved)
    ├── tests/                     # Original test files
    └── processors/                # Original processors
```

## 🔄 Workflow Examples

### Complete Geological Analysis
```python
import requests

# Upload core sample image
with open('core_sample.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8386/process',
        files={'file': f},
        data={
            'use_segmentation': 'true',
            'depth_from': '0.0',
            'depth_to': '3.0',
            'custom_prompt': 'Analyze geological features and rock types'
        }
    )

result = response.json()
print(f"Found {len(result['x'])} segments")
print(f"OCR results: {result['google_result']}")
print(f"AI analysis: {result['gemini_result']}")
```

### Segmentation and Cropping
```python
# Get segmented and cropped images
response = requests.post(
    'http://localhost:8388/segment_crop',
    files={'file': open('core_sample.jpg', 'rb')}
)

result = response.json()
segments = result['detection']['predictions']
print(f"Detected {len(segments)} segments")
```

## 🛠️ Development

### Adding New Features

1. **New Service Integration:**
   - Add service class in `services/`
   - Update configuration in `config/settings.py`
   - Add unit tests in `tests/unit/test_services.py`

2. **New API Endpoint:**
   - Create Flask app in `apis/`
   - Add integration tests in `tests/integration/`
   - Update documentation

3. **New Processing Pipeline:**
   - Add processor in `processors/`
   - Integrate with existing services
   - Add comprehensive tests

### Code Quality

- **Linting:** Use `flake8` for code style
- **Type Hints:** Use type annotations where possible
- **Documentation:** Document all public methods
- **Testing:** Maintain >90% test coverage

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 🔧 Dependencies

### Core Dependencies
```
Flask==2.3.3              # Web framework for APIs
requests==2.31.0           # HTTP client for external APIs
opencv-python==********   # Computer vision and image processing
numpy==1.24.3              # Numerical computing
Pillow==10.0.1             # Image processing library
python-dotenv==1.0.0       # Environment variable management
```

### External Service Dependencies
```
google-cloud-vision==3.4.4    # Google Vision OCR API
google-generativeai==0.3.0    # Gemini AI API
azure-storage-blob==12.17.0   # Azure Blob Storage
roboflow==1.1.0               # Roboflow ML models
```

### Testing Dependencies
```
pytest==7.4.2                 # Testing framework
pytest-cov==4.1.0            # Coverage reporting
pytest-mock==3.11.1          # Mocking utilities
matplotlib==3.7.2            # Visualization for performance tests
psutil==5.9.5                # System resource monitoring
```

### Development Dependencies
```
flake8==6.0.0                # Code linting
black==23.7.0                # Code formatting
mypy==1.5.1                  # Type checking
```

## 📚 Documentation

- **API Flow Charts:** [docs/flow-charts.md](docs/flow-charts.md)
- **Integration Tests:** [tests/integration/README.md](tests/integration/README.md)
- **Performance Testing:** [tests/performance/README.md](tests/performance/README.md)
- **API Testing Tools:** [testing/README.md](testing/README.md)

## 🐛 Troubleshooting

### Common Issues

**APIs not starting:**
```bash
# Check if ports are available
netstat -tulpn | grep :8386

# Check logs for errors
python apis/main_processing.py

# Verify Python dependencies
pip install -r requirements.txt
```

**External service errors:**
```bash
# Verify API keys are set
echo $ROBOFLOW_API_KEY
echo $GOOGLE_VISION_API_KEY
echo $GEMINI_API_KEY

# Test service connectivity
python -c "from services.roboflow_service import RoboflowService; print('Roboflow OK')"
python -c "from services.google_vision import GoogleVisionService; print('Google Vision OK')"
python -c "from services.gemini_service import GeminiService; print('Gemini OK')"
```

**Image processing errors:**
```bash
# Check OpenCV installation
python -c "import cv2; print(f'OpenCV version: {cv2.__version__}')"

# Test image processing
python -c "from services.image_processing import ImageProcessingService; print('Image processing OK')"

# Verify image format support
python -c "from PIL import Image; print('PIL formats:', Image.registered_extensions())"
```

**Performance issues:**
```bash
# Run performance diagnostics
python tests/performance/run_performance_tests.py --test-type benchmark --requests 5

# Monitor resource usage
python -c "from tests.performance import PerformanceMonitor; m=PerformanceMonitor(); m.start_monitoring()"

# Check system resources
python -c "import psutil; print(f'CPU: {psutil.cpu_percent()}%, Memory: {psutil.virtual_memory().percent}%')"
```

**Configuration issues:**
```bash
# Verify configuration loading
python -c "from config.settings import get_settings; s=get_settings(); print('Config loaded successfully')"

# Check environment variables
python -c "import os; print('ROBOFLOW_API_KEY:', 'SET' if os.getenv('ROBOFLOW_API_KEY') else 'NOT SET')"

# Test configuration values
python -c "from config.settings import get_settings; print(get_settings().__dict__)"
```

### Error Codes and Solutions

**HTTP 500 - Internal Server Error:**
- Check API logs for detailed error messages
- Verify all required environment variables are set
- Test external service connectivity
- Check image format and size limits

**HTTP 400 - Bad Request:**
- Verify request format (multipart/form-data for file uploads)
- Check required parameters are provided
- Validate image file format (JPEG, PNG supported)
- Ensure file size is within limits

**Timeout Errors:**
- Check network connectivity to external services
- Increase timeout values in configuration
- Monitor system resource usage
- Consider reducing image size for processing

**Memory Errors:**
- Monitor memory usage during processing
- Reduce image size or batch size
- Check for memory leaks in long-running processes
- Ensure adequate system memory (4GB+ recommended)

### Getting Help

1. **Check Logs:** Review API logs for detailed error messages
2. **Verify Setup:** Ensure all environment variables and dependencies are configured
3. **Test Services:** Use individual service tests to isolate issues
4. **Health Checks:** Run health checks on all APIs and external services
5. **Performance Tests:** Use performance testing to identify bottlenecks
6. **Documentation:** Review component-specific documentation in respective directories

### Debug Mode

Enable debug mode for detailed logging:
```bash
# Set debug environment variable
export FLASK_DEBUG=1
export LOG_LEVEL=DEBUG

# Run APIs with debug output
python apis/main_processing.py
```

## 🚀 Deployment

### Production Deployment

**Using Docker (Recommended):**
```bash
# Build Docker images
docker build -t aibase-ml-main -f Dockerfile.main .
docker build -t aibase-ml-segment -f Dockerfile.segment .

# Run with Docker Compose
docker-compose up -d
```

**Manual Deployment:**
```bash
# Install production dependencies
pip install -r requirements.txt
pip install gunicorn

# Run with Gunicorn
gunicorn --bind 0.0.0.0:8386 --workers 4 apis.main_processing:app
gunicorn --bind 0.0.0.0:8388 --workers 4 apis.segment_auto_crop:app
gunicorn --bind 0.0.0.0:8381 --workers 4 apis.segment_core_pieces:app
gunicorn --bind 0.0.0.0:8389 --workers 4 apis.segment_core_server:app
```

**Environment Configuration:**
```bash
# Production environment variables
export FLASK_ENV=production
export LOG_LEVEL=INFO
export MAX_WORKERS=4
export TIMEOUT=300
```

### Scaling Considerations

- **Load Balancing:** Use nginx or similar for load balancing across multiple instances
- **Caching:** Implement Redis caching for frequently accessed data
- **Database:** Consider PostgreSQL for persistent data storage
- **Monitoring:** Use Prometheus/Grafana for production monitoring
- **Auto-scaling:** Configure auto-scaling based on CPU/memory usage

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Support

For support and questions:
- **Documentation:** Check component-specific documentation in respective directories
- **Examples:** Review test examples in `testing/` directory
- **Diagnostics:** Run diagnostic tests with the comprehensive testing suite
- **Health Checks:** Monitor API health endpoints for real-time service status
- **Performance:** Use performance testing tools to identify and resolve bottlenecks

## 🔄 Version History

- **v2.0.0** - Complete refactoring with clean architecture, comprehensive testing, and performance monitoring
- **v1.x.x** - Original experimental implementation with segment_*.py files

---

**Built with ❤️ for geological analysis and core sample processing**
