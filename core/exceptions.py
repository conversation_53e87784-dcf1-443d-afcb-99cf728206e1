"""Custom exceptions for aibase-ml application."""

from typing import Optional, Dict, Any


class AibaseMLException(Exception):
    """Base exception for all aibase-ml related errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ValidationError(AibaseMLException):
    """Raised when input validation fails."""
    pass


class ProcessingError(AibaseMLException):
    """Raised when image processing fails."""
    pass


class ExternalServiceError(AibaseMLException):
    """Raised when external service calls fail."""
    pass


class ImageProcessingError(ProcessingError):
    """Raised when image processing operations fail."""
    pass


class ModelInferenceError(ProcessingError):
    """Raised when model inference fails."""
    pass


class AzureStorageError(ExternalServiceError):
    """Raised when Azure storage operations fail."""
    pass


class RoboflowAPIError(ExternalServiceError):
    """Raised when Roboflow API calls fail."""
    pass


class GoogleVisionError(ExternalServiceError):
    """Raised when Google Vision API calls fail."""
    pass


class GeminiAPIError(ExternalServiceError):
    """Raised when Gemini API calls fail."""
    pass
