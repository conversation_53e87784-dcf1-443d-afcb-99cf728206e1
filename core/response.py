"""Standardized API response handling for aibase-ml application."""

from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, asdict
from flask import jsonify, Response
import time


@dataclass
class APIResponse:
    """Base class for API responses."""
    
    success: bool
    timestamp: float
    execution_time_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary."""
        return asdict(self)
    
    def to_flask_response(self, status_code: int = 200) -> Response:
        """Convert to Flask response."""
        return jsonify(self.to_dict()), status_code


@dataclass
class SuccessResponse(APIResponse):
    """Success response with data."""
    
    data: Any
    message: Optional[str] = None
    
    def __init__(
        self, 
        data: Any, 
        message: Optional[str] = None,
        execution_time_ms: Optional[float] = None
    ):
        super().__init__(
            success=True,
            timestamp=time.time(),
            execution_time_ms=execution_time_ms
        )
        self.data = data
        self.message = message


@dataclass
class ErrorResponse(APIResponse):
    """Error response with error details."""
    
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    
    def __init__(
        self, 
        error: str, 
        message: str,
        details: Optional[Dict[str, Any]] = None,
        execution_time_ms: Optional[float] = None
    ):
        super().__init__(
            success=False,
            timestamp=time.time(),
            execution_time_ms=execution_time_ms
        )
        self.error = error
        self.message = message
        self.details = details or {}


def create_success_response(
    data: Any,
    message: Optional[str] = None,
    execution_time_ms: Optional[float] = None,
    status_code: int = 200
) -> Response:
    """Create a standardized success response."""
    response = SuccessResponse(
        data=data,
        message=message,
        execution_time_ms=execution_time_ms
    )
    return response.to_flask_response(status_code)


def create_error_response(
    error: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    execution_time_ms: Optional[float] = None,
    status_code: int = 400
) -> Response:
    """Create a standardized error response."""
    response = ErrorResponse(
        error=error,
        message=message,
        details=details,
        execution_time_ms=execution_time_ms
    )
    return response.to_flask_response(status_code)


def create_validation_error_response(
    message: str,
    field_errors: Optional[Dict[str, str]] = None,
    execution_time_ms: Optional[float] = None
) -> Response:
    """Create a validation error response."""
    return create_error_response(
        error="ValidationError",
        message=message,
        details={"field_errors": field_errors or {}},
        execution_time_ms=execution_time_ms,
        status_code=400
    )


def create_processing_error_response(
    message: str,
    processing_stage: Optional[str] = None,
    execution_time_ms: Optional[float] = None
) -> Response:
    """Create a processing error response."""
    return create_error_response(
        error="ProcessingError",
        message=message,
        details={"processing_stage": processing_stage},
        execution_time_ms=execution_time_ms,
        status_code=500
    )
