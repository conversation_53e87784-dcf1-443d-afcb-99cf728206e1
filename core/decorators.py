"""Common decorators for aibase-ml application."""

import time
import functools
from typing import Callable, Any, Dict, Optional
from flask import request, Response

from .exceptions import AibaseMLException, ValidationError
from .response import create_error_response, create_validation_error_response
from config.logging import get_logger

logger = get_logger(__name__)


def handle_errors(func: Callable) -> Callable:
    """
    Decorator to handle exceptions and return standardized error responses.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            execution_time = (time.time() - start_time) * 1000
            logger.warning(f"Validation error in {func.__name__}: {e.message}")
            return create_validation_error_response(
                message=e.message,
                field_errors=e.details.get("field_errors"),
                execution_time_ms=execution_time
            )
        except AibaseMLException as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Application error in {func.__name__}: {e.message}")
            return create_error_response(
                error=e.error_code,
                message=e.message,
                details=e.details,
                execution_time_ms=execution_time,
                status_code=500
            )
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            return create_error_response(
                error="InternalServerError",
                message="An unexpected error occurred",
                details={"original_error": str(e)},
                execution_time_ms=execution_time,
                status_code=500
            )
    return wrapper


def validate_request(required_files: Optional[list] = None, required_form: Optional[list] = None) -> Callable:
    """
    Decorator to validate request data.
    
    Args:
        required_files: List of required file field names
        required_form: List of required form field names
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Validate required files
            if required_files:
                for file_field in required_files:
                    if file_field not in request.files:
                        raise ValidationError(
                            f"Missing required file: {file_field}",
                            details={"field_errors": {file_field: "This field is required"}}
                        )
                    
                    file = request.files[file_field]
                    if file.filename == '':
                        raise ValidationError(
                            f"No file selected for: {file_field}",
                            details={"field_errors": {file_field: "No file selected"}}
                        )
            
            # Validate required form fields
            if required_form:
                for form_field in required_form:
                    if form_field not in request.form:
                        raise ValidationError(
                            f"Missing required form field: {form_field}",
                            details={"field_errors": {form_field: "This field is required"}}
                        )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def log_execution_time(func: Callable) -> Callable:
    """
    Decorator to log function execution time.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = (time.time() - start_time) * 1000
        logger.info(f"{func.__name__} executed in {execution_time:.2f}ms")
        return result
    return wrapper


def validate_numeric_params(**param_configs: Dict[str, Any]) -> Callable:
    """
    Decorator to validate numeric parameters from request form.
    
    Args:
        param_configs: Dictionary mapping parameter names to their configuration
                      e.g., {"depth_from": {"type": float, "default": 0.0, "min": 0}}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            field_errors = {}
            
            for param_name, config in param_configs.items():
                param_type = config.get("type", str)
                default_value = config.get("default")
                min_value = config.get("min")
                max_value = config.get("max")
                
                try:
                    # Get value from form or use default
                    raw_value = request.form.get(param_name)
                    if raw_value is None:
                        if default_value is not None:
                            kwargs[param_name] = default_value
                            continue
                        else:
                            field_errors[param_name] = "This field is required"
                            continue
                    
                    # Convert to specified type
                    if param_type == bool:
                        value = raw_value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = param_type(raw_value)
                    
                    # Validate range
                    if min_value is not None and value < min_value:
                        field_errors[param_name] = f"Value must be >= {min_value}"
                        continue
                    
                    if max_value is not None and value > max_value:
                        field_errors[param_name] = f"Value must be <= {max_value}"
                        continue
                    
                    kwargs[param_name] = value
                    
                except (ValueError, TypeError):
                    field_errors[param_name] = f"Invalid {param_type.__name__} value"
            
            if field_errors:
                raise ValidationError(
                    "Invalid request parameters",
                    details={"field_errors": field_errors}
                )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
