import os
import uuid
from flask import Flask, request, jsonify
from utils.image import correct_image_orientation, get_resize_scale_ratio
from utils.roboflow import call_roboflow_api
from processors.detection import assign_row_index, upscale_detection_coords
from processors.segmentation import (
    process_segmentation, 
    process_segment_with_flag, 
    upscale_segmentation_coords
)

app = Flask(__name__)

def update_google_vision_with_gemini(response, width_ratio, height_ratio):
    if "segmentation" not in response or "google_vision_block_ocr" not in response or "google_gemini_block_crop" not in response:
        return response

    # Get all valid boxes
    from processors.detection import find_valid_boxes
    valid_boxes = find_valid_boxes(response['detection'])
    box_mapping = {box["detection_id"]: box for box in valid_boxes}

    # Build segmentation -> metadata dictionary
    segmentation_dict = {}
    for seg in response["segmentation"]:
        box_id = seg.get("boxId")
        if box_id and box_id in box_mapping:
            box = box_mapping[box_id]
            box_x = int(box["x_raw"] - box["width"] / 2)
            box_y = int(box["y_raw"] - box["height"] / 2)

            segmentation_dict[seg["detection_id"]] = {
                "x": (seg["x"] + box_x) * width_ratio,
                "y": (seg["y"] + box_y) * height_ratio,
                "width": seg["width"] * width_ratio,
                "height": seg["height"] * height_ratio,
                "rowIndex": seg.get("rowIndex", 0),
                "boxId": box_id
            }
        else:
            segmentation_dict[seg["detection_id"]] = {
                "x": seg["x"] * width_ratio,
                "y": seg["y"] * height_ratio,
                "width": seg["width"] * width_ratio,
                "height": seg["height"] * height_ratio,
                "rowIndex": seg.get("rowIndex", 0)
            }
    
    # Update OCR blocks with the rescaled values
    updated_ocr_blocks = []
    for i, ocr_block in enumerate(response["google_vision_block_ocr"][0]):
        for j, prediction in enumerate(ocr_block["predictions"]["predictions"]):
            parent_id = prediction["parent_id"]

            updated_block = {
                "text": ocr_block["text"],
                "language": ocr_block["language"],
            }

            if parent_id in segmentation_dict:
                updated_block.update(segmentation_dict[parent_id])

            updated_block["gemini_text"] = response["google_gemini_block_crop"][0][i]["output"].replace(" ", "").replace("\n", "").strip()
            updated_ocr_blocks.append(updated_block)
            break

    response["google_vision_block_ocr"] = updated_ocr_blocks
    return response

@app.route("/process", methods=["POST"])
def process_image():
    """ API endpoint to process an image and apply Roboflow processing """
    if "file" not in request.files:
        return jsonify({"error": "No file uploaded"}), 400

    try:
        depth_from = float(request.form.get("depth_from", 0))
        depth_to = float(request.form.get("depth_to", 0))
        user_prompt = request.form.get("prompt", None)
        segment_flag = request.form.get("segment_flag", False)
    except ValueError:
        return jsonify({"error": "depth_from and depth_to must be numeric"}), 400

    if segment_flag == "True" or segment_flag == "true":
        segment_flag = True
    else:
        segment_flag = False
        
    file = request.files["file"]
    file_path = f"./temp_{uuid.uuid4().hex}.jpg"
    file.save(file_path)

    correct_image_orientation(file_path, save_path=file_path)
    try:
        roboflow_response = call_roboflow_api(file_path, depth_from, depth_to, user_prompt, segment_flag)
        width_ratio, height_ratio = get_resize_scale_ratio(file_path)
        
        if "detection" in roboflow_response:
            roboflow_response["detection"]["predictions"] = assign_row_index(roboflow_response["detection"])

        if segment_flag:
            segmentation_result = process_segment_with_flag(roboflow_response, file_path)
        else:
            segmentation_result = process_segmentation(roboflow_response, file_path)

        if roboflow_response.get("segmentation", []):
            roboflow_response["segmentation"] = segmentation_result
            roboflow_response["segmentation"] = sorted(
                roboflow_response["segmentation"], 
                key=lambda x: ((x.get("boxId", ""), x.get("rowIndex", float('inf'))) 
                              if x.get("class") in ["core", "Block"] else (x.get("boxId", ""), float('inf')))
            )
        else:
            roboflow_response["segmentation"] = segmentation_result
            
            if any("rowIndex" in seg for seg in roboflow_response["segmentation"]):
                roboflow_response["segmentation"] = sorted(
                    roboflow_response["segmentation"], 
                    key=lambda x: ((x.get("boxId", ""), x.get("rowIndex", float('inf'))) 
                                  if isinstance(x, dict) and "rowIndex" in x and x.get("class") in ["core", "Block"] 
                                  else (x.get("boxId", ""), float('inf')))
                )
            
        roboflow_response = update_google_vision_with_gemini(roboflow_response, width_ratio, height_ratio)
        roboflow_response["detection"]["predictions"] = upscale_detection_coords(
            roboflow_response["detection"]["predictions"], width_ratio, height_ratio
        )
        roboflow_response["segmentation"] = upscale_segmentation_coords(
            roboflow_response["segmentation"], width_ratio, height_ratio
        )
        roboflow_response["segmentation"] = sorted(
            roboflow_response['segmentation'], 
            key=lambda bbox: bbox['rowIndex']
        )

    except Exception as err:
        print(err)
        os.remove(file_path)
        return jsonify({})
        
    os.remove(file_path)
    return jsonify(roboflow_response)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8386, debug=True)