import time
from inference_sdk import InferenceHTTPClient
from .image import encode_resized_base64

client = InferenceHTTPClient(
    api_url="https://detect.roboflow.com",
    api_key="hM2hVNK6d4UFxGMXjXkT"
)

WORKSPACE_NAME = "depth-registration"
WORKFLOW_ID = "allinone"

def process_response(data_list):
    for data in data_list:
        if data.get("segment_flag") is True:
            # Remove keys if they exist
            data.pop("detection", None)
            data.pop("segmentation", None)

            # Rename detection_flag → detection
            data["detection"] = data.pop("detection_flag", None)

            gg_ocr = data.pop("google_vision_ocr", None)
            if gg_ocr is not None:
                if isinstance(gg_ocr, list):
                    data["google_vision_block_ocr"] = [gg_ocr]
                else:
                    data["google_vision_block_ocr"] = [[gg_ocr]]

            gemini = data.pop("google_gemini", None)
            if gemini is not None:
                if isinstance(gemini, list):
                    data["google_gemini_block_crop"] = [gemini]
                else:
                    data["google_gemini_block_crop"] = [[gemini]]

            # Rename model_1_predictions → segmentation (as a list)
            model_preds = data.pop("model_1_predictions", None)
            if model_preds is not None:
                if isinstance(model_preds, list):
                    data["segmentation"] = model_preds
                else:
                    data["segmentation"] = [model_preds]
    
    return data_list

def call_roboflow_api(image_path, depth_from, depth_to, user_prompt=None, segment_flag=False, max_retries=10):
    # Default Prompt
    default_prompt = f"Perform OCR on the given image and extract the text. Identify the most probable depth value within the detected text, ensuring it falls within the range of {depth_from} to {depth_to} .The depth value may appear in various formats, such as 'HD: xx.xx'. However, some values might be incorrectly formatted, such as '2-70', '2/70', or '2*70'. Convert these into the correct format as '2.70' before performing the comparison. If no clear depth value is found, analyze the detected text and infer the most likely depth within the given range based on contextual clues. Return only the depth value as a number (e.g., 5.5, 10.43, 1.2). Do not return values with additional characters such as '5.5/n' or '1.2/n'. If the text does not contain a valid depth value, return 'Not found'. Do not include any extra text, units, or explanations."
    
    custom_prompt = f"""
        Perform OCR on the given image and extract any numeric values that may represent depth. The depth value may appear in various formats, 
such as 'HD: xx.xx', or standalone numbers like 'CIL x.x' and 'CIL x.x' but priority is HD: xx.xx or sometime you can detect as HO, H0, HB,...
        
        Some values may be incorrectly formatted, such as '2-70', '2/70', or '2*70'. Convert these into the correct format as '2.70' before performing the comparison.
        
        ### **Rules for Extraction:**
        - Number must be falls **within the range of {depth_from} to {depth_to}**, return it as the most probable depth.
        - If **multiple depth candidates** exist, prioritize the value that follows a common depth-related keyword (e.g., **HD, D, R, CIL**).
        - If a number appears alone (e.g., **CIL 0.9**), but no clear depth label exists, **still consider it if it matches the range**.
        - If a depth value is found **with surrounding words**, extract only the number (e.g., from 'HD: 120.6', return only `120.6`).
        - If no valid depth values exist and the text contains **only words**, return `'Not found'`.
        
        ### **Output Rules:**
        - Return **only** the depth value as a number (e.g., `5.5`, `10.43`, `1.2`).
        - Ensure **no extra characters** like `5.5\n`, `1.2\n`, or `120.6 HD`—return only the number.
        - Trim whitespace for the final result
        - Do not exaplain any thing, just numbers
        """

    # Use user-provided prompt or default
    final_prompt = user_prompt if user_prompt else custom_prompt

    for attempt in range(1, max_retries + 1):
        try:
            result = client.run_workflow(
                workspace_name=WORKSPACE_NAME,
                workflow_id=WORKFLOW_ID,
                images={"image": encode_resized_base64(image_path)},
                parameters={"segment_flag": segment_flag, "block_prompt": final_prompt},
                use_cache=True
            )
            
            if segment_flag == True:
                result = process_response(result)
            
            if "predictions" in result[0]["detection"]:
                for pred in result[0]["detection"]["predictions"]:
                    pred["x_raw"] = pred["x"]
                    pred["y_raw"] = pred["y"]

                    pred["x"] = pred["x"] - pred["width"] / 2  
                    pred["y"] = pred["y"] - pred["height"] / 2  

            return result[0]  # Success, return the response

        except Exception as e:
            print(f"Attempt {attempt}/{max_retries} failed: {e}")
            if attempt < max_retries:
                wait_time = 8  # Exponential backoff (1s, 2s, 4s, 8s, ...)
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                print("Max retries reached. Returning error response.")
                return {"error": "Failed to fetch results from Roboflow API after multiple attempts."}