import base64
import io
from PIL import Image, ExifTags

def correct_image_orientation(image_path, save_path=None):
    try:
        img = Image.open(image_path)

        exif = img._getexif()
        if not exif:
            print("No EXIF data found. Returning original image.")
            return img

        orientation_key = next(
            (key for key, val in ExifTags.TAGS.items() if val == 'Orientation'), None
        )

        orientation = exif.get(orientation_key, 1)

        if orientation == 3:
            img = img.rotate(180, expand=True)
            print("Rotated 180°")
        elif orientation == 6:
            img = img.rotate(270, expand=True)
            print("Rotated 270° (90° CW)")
        elif orientation == 8:
            img = img.rotate(90, expand=True)
            print("Rotated 90° (90° CCW)")
        else:
            print("Orientation is normal or not recognized. No rotation applied.")

        # Optionally save the corrected image
        if save_path:
            img.save(save_path)

    except Exception as e:
        print(f"Error correcting image orientation: {e}")

def encode_resized_base64(image_path, max_size=(2048, 2048)):
    with Image.open(image_path) as img:
        img.thumbnail(max_size)  # Resize while preserving aspect ratio

        buffer = io.BytesIO()
        img.save(buffer, format="JPEG")
        image_bytes = buffer.getvalue()

        return base64.b64encode(image_bytes).decode("ascii")

def get_resize_scale_ratio(image_path, max_size=(2048, 2048)):
    with Image.open(image_path) as img:
        orig_width, orig_height = img.size

        # Simulate the thumbnail operation to get resized dimensions
        img_copy = img.copy()
        img_copy.thumbnail(max_size)
        resized_width, resized_height = img_copy.size

        width_ratio = orig_width / resized_width
        height_ratio = orig_height / resized_height

        return width_ratio, height_ratio