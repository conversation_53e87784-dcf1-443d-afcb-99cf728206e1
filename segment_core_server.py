from flask import Flask, request, jsonify
import cv2
import numpy as np
from copy import deepcopy
import time
import base64
import requests
import json
import uuid
from datetime import datetime
from inference import get_roboflow_model
from google import genai
from google.genai import types
from azure.storage.blob import BlobServiceClient

app = Flask(__name__)

class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

app.json_encoder = NumpyEncoder

# API Keys and Configuration
ROBOFLOW_API_KEY = 'hM2hVNK6d4UFxGMXjXkT'
GOOGLE_VISION_API_KEY = 'AIzaSyDCnITbjPc9xQckAz3_xD37v0-v2ScAUTc'
GEMINI_API_KEY = 'AIzaSyABOdc6lzJpu77O_M5IAWUUBdfeFQ-dja0'

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=aibaseimagestorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AZURE_CONTAINER_NAME = "images-stag"

def upload_image_to_azure(image_array, filename=None):
    """Upload an image array to Azure Blob Storage"""
    try:
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"warped_image_{timestamp}_{unique_id}.jpg"

        success, buffer = cv2.imencode('.jpg', image_array)
        if not success:
            return {"success": False, "error": "Failed to encode image"}

        image_bytes = buffer.tobytes()
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        blob_client = blob_service_client.get_blob_client(
            container=f"{AZURE_CONTAINER_NAME}/warped_image",
            blob=filename
        )

        blob_client.upload_blob(image_bytes, overwrite=True, content_type='image/jpeg')
        blob_url = f"https://aibaseimagestorage.blob.core.windows.net/{AZURE_CONTAINER_NAME}/warped_image/{filename}"

        return {
            "success": True,
            "url": blob_url,
            "filename": filename
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def run_box_core_block_segment(image):
    """Run Roboflow segmentation model on image"""
    start = time.time()
    model = get_roboflow_model(model_id="box-core-block-segmentation/6", api_key=ROBOFLOW_API_KEY)
    result = model.infer(image)
    elapsed = time.time() - start
    return "Detection", result[0].dict(), elapsed

def transform_format(segmentation_data):
    """Transform segmentation data to required format"""
    processed_segmentations = []
    for pred in segmentation_data.get("predictions", []):
        if pred['confidence'] < 0.5:
            continue
        segment = {
            "class": pred["class_name"],
            "x": float(pred["x"]),
            "y": float(pred["y"]),
            "width": float(pred["width"]),
            "height": float(pred["height"]),
            "confidence": float(pred["confidence"]),
        }
        processed_segmentations.append(segment)
    return processed_segmentations

def scale_contour(contour: np.ndarray, scale: float) -> np.ndarray:
    """Scales a contour around its center."""
    M = cv2.moments(contour)
    if M['m00'] == 0:
        return contour
    cx = int(M['m10'] / M['m00'])
    cy = int(M['m01'] / M['m00'])
    center = np.array([[cx, cy]])
    scaled = (contour - center) * scale + center
    return scaled.astype(np.int32)

def merge_boxes_as_one_segment(segments, merged_class='Box'):
    """Merges all 'Box' segments into a single large segment using convex hull."""
    box_segments = [seg for seg in segments if seg['class'].lower() == 'box']
    other_segments = [seg for seg in segments if seg['class'].lower() != 'box']

    if not box_segments:
        return segments

    # Calculate bounding box that encompasses all box segments
    confidences = []
    x_mins, y_mins, x_maxs, y_maxs = [], [], [], []

    for seg in box_segments:
        confidences.append(seg['confidence'])
        # Calculate bounding box corners from center, width, height
        x_min = seg['x'] - seg['width'] / 2
        x_max = seg['x'] + seg['width'] / 2
        y_min = seg['y'] - seg['height'] / 2
        y_max = seg['y'] + seg['height'] / 2

        x_mins.append(x_min)
        x_maxs.append(x_max)
        y_mins.append(y_min)
        y_maxs.append(y_max)

    # Find overall bounding box
    x_min, x_max = min(x_mins), max(x_maxs)
    y_min, y_max = min(y_mins), max(y_maxs)

    merged_segment = {
        "class": merged_class,
        "confidence": float(np.mean(confidences)),
        "x": float((x_min + x_max) / 2),
        "y": float((y_min + y_max) / 2),
        "width": float(x_max - x_min),
        "height": float(y_max - y_min),
    }

    return other_segments + [merged_segment]

def filter_segments_inside_box(segments, merged_box_segment):
    """Filters out segments that are not inside the merged 'Box' segment."""
    # Use bounding box instead of points for filtering
    box_x_min = merged_box_segment['x'] - merged_box_segment['width'] / 2
    box_x_max = merged_box_segment['x'] + merged_box_segment['width'] / 2
    box_y_min = merged_box_segment['y'] - merged_box_segment['height'] / 2
    box_y_max = merged_box_segment['y'] + merged_box_segment['height'] / 2

    filtered_segments = []

    for seg in segments:
        if seg is merged_box_segment:
            filtered_segments.append(seg)
            continue

        # Check if segment center is inside the box
        seg_x, seg_y = seg['x'], seg['y']
        if (box_x_min <= seg_x <= box_x_max and
            box_y_min <= seg_y <= box_y_max):
            filtered_segments.append(seg)

    return filtered_segments

def apply_box_transform_to_segments(filter_seg, original_image, extent=10):
    """Applies the perspective transform from the largest 'Box' to all segments."""
    box = next(item for item in filter_seg if item['class'].lower() == 'box')
    others = [item for item in filter_seg if item['class'].lower() != 'box']

    # Generate box corner points from bounding box
    box_x_min = int(box['x'] - box['width'] / 2)
    box_x_max = int(box['x'] + box['width'] / 2)
    box_y_min = int(box['y'] - box['height'] / 2)
    box_y_max = int(box['y'] + box['height'] / 2)

    box_points = np.array([
        [box_x_min, box_y_min],
        [box_x_max, box_y_min],
        [box_x_max, box_y_max],
        [box_x_min, box_y_max]
    ], dtype=np.int32)

    hull_points = scale_contour(box_points, 1 + extent / 100)

    rect = cv2.minAreaRect(hull_points)
    box_points = cv2.boxPoints(rect).astype(np.int32)
    bbox = cv2.boundingRect(box_points)
    bbox = [max(0, x) for x in bbox]
    bbox_points = np.array([
        [bbox[0], bbox[1]],
        [bbox[0] + bbox[2], bbox[1]],
        [bbox[0] + bbox[2], bbox[1] + bbox[3]],
        [bbox[0], bbox[1] + bbox[3]]
    ], dtype=np.float32)

    epsilon = 0.02 * cv2.arcLength(hull_points, True)
    approx = cv2.approxPolyDP(hull_points, epsilon, True)

    src = []
    for bb_point in bbox_points:
        min_dis = float("inf")
        min_point = bb_point
        for point in approx:
            dis = np.linalg.norm(point.ravel() - bb_point)
            if dis < min_dis:
                min_dis = dis
                min_point = point.ravel()
        src.append(min_point)
    src = np.array(src, dtype=np.float32)

    dst = np.array([[0, 0], [bbox[2], 0], [bbox[2], bbox[3]], [0, bbox[3]]], dtype=np.float32)

    M = cv2.getPerspectiveTransform(src, dst)
    warped = cv2.warpPerspective(original_image, M, (bbox[2], bbox[3]))

    transformed_segments = []

    for seg in [box] + others:
        # Generate corner points from bounding box
        seg_x_min = seg['x'] - seg['width'] / 2
        seg_x_max = seg['x'] + seg['width'] / 2
        seg_y_min = seg['y'] - seg['height'] / 2
        seg_y_max = seg['y'] + seg['height'] / 2

        seg_points = np.array([
            [seg_x_min, seg_y_min],
            [seg_x_max, seg_y_min],
            [seg_x_max, seg_y_max],
            [seg_x_min, seg_y_max]
        ], dtype=np.float32).reshape(-1, 1, 2)

        warped_pts = cv2.perspectiveTransform(seg_points, M).reshape(-1, 2)
        xs, ys = warped_pts[:, 0], warped_pts[:, 1]

        transformed_segments.append({
            'class': seg['class'],
            'confidence': float(seg['confidence']),
            'x': float(np.mean(xs)),
            'y': float(np.mean(ys)),
            'width': float(np.max(xs) - np.min(xs)),
            'height': float(np.max(ys) - np.min(ys)),
        })

    return warped, transformed_segments

# Function removed - no longer needed since we don't use points

def crop_boxes_from_segments(segments, image, y_tolerance=50):
    """Extract and sort cropped regions of 'Block' class from the original image."""
    box_entries = []

    for seg in segments:
        if seg.get("class") == "Block":
            # Use bounding box coordinates directly
            x = int(seg['x'] - seg['width'] / 2)
            y = int(seg['y'] - seg['height'] / 2)
            w = int(seg['width'])
            h = int(seg['height'])

            x_end = min(x + w, image.shape[1])
            y_end = min(y + h, image.shape[0])
            x_start = max(x, 0)
            y_start = max(y, 0)

            cropped = image[y_start-10:y_end+10, x_start-10:x_end+10].copy()

            box_entries.append({
                "x": int(seg['x']),
                "y": int(seg['y']),
                "image": cropped
            })

    # Sort by y (top to bottom)
    box_entries.sort(key=lambda entry: entry["y"])

    # Group by rows (using y_tolerance)
    rows = []
    current_row = []
    last_y = None

    for entry in box_entries:
        if last_y is None or abs(entry["y"] - last_y) <= y_tolerance:
            current_row.append(entry)
        else:
            rows.append(current_row)
            current_row = [entry]
        last_y = entry["y"]

    if current_row:
        rows.append(current_row)

    # Sort each row by x (left to right)
    sorted_images = []
    for row in rows:
        row_sorted = sorted(row, key=lambda entry: entry["x"])
        sorted_images.extend([entry["image"] for entry in row_sorted])

    return sorted_images

def run_google_vision_on_cropped_images(cropped_images, api_key):
    """Run Google OCR on a list of cropped images."""
    results = []
    start_time = time.time()

    for idx, img in enumerate(cropped_images):
        success, encoded_image = cv2.imencode(".jpg", img)
        if not success:
            results.append(f"[{idx}] Encoding failed")
            continue

        content = base64.b64encode(encoded_image.tobytes()).decode("utf-8")
        request_payload = {
            "requests": [{
                "image": {"content": content},
                "features": [{"type": "TEXT_DETECTION"}]
            }]
        }
        url = f"https://vision.googleapis.com/v1/images:annotate?key={api_key}"
        response = requests.post(url, headers={"Content-Type": "application/json"}, json=request_payload)

        if response.status_code == 200:
            json_data = response.json()
            text = json_data.get("responses", [{}])[0].get("fullTextAnnotation", {}).get("text", "No text")
            results.append(text.strip())
        else:
            results.append(f"[{idx}] Error {response.status_code}")

    return "Google OCR", results, time.time() - start_time

def run_gemini_prompt_on_cropped_images(cropped_images, prompt, api_key):
    """Run Gemini model on a list of cropped images with the same prompt."""
    client = genai.Client(api_key=api_key)
    responses = []
    start_time = time.time()

    for idx, img in enumerate(cropped_images):
        try:
            success, encoded_image = cv2.imencode(".jpg", img)
            if not success:
                responses.append(f"[{idx}] Encoding failed")
                continue

            image_bytes = encoded_image.tobytes()

            response = client.models.generate_content(
                model='gemini-2.0-flash-lite',
                contents=[
                    types.Part.from_bytes(data=image_bytes, mime_type='image/jpeg'),
                    prompt
                ]
            )
            responses.append(response.text.strip())
        except Exception as e:
            responses.append(f"[{idx}] Error: {str(e)}")

    return "Gemini", responses, time.time() - start_time

@app.route('/process_core_outline', methods=['POST'])
def process_core_outline():
    """Main endpoint to process image and return all results including warped image, Google OCR, and Gemini results"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No image file selected'}), 400

    # Get optional parameters
    use_row = request.form.get('use_row', 'true').lower() == 'true'  # Default to true like original
    depth_from = float(request.form.get('depth_from', 0))
    depth_to = float(request.form.get('depth_to', 2.4))

    # Custom prompt for Gemini
    custom_prompt = request.form.get('custom_prompt', f"""
        Perform OCR on the given image and extract any numeric values that may represent depth. The depth value may appear in various formats,
        such as 'HD: xx.xx', or standalone numbers like 'CIL x.x' and 'CIL x.x' but priority is HD: xx.xx or sometime you can detect as HO, H0, HB,...

        Some values may be incorrectly formatted, such as '2-70', '2/70', or '2*70'. Convert these into the correct format as '2.70' before performing the comparison.

        ### **Rules for Extraction:**
        - Number must be falls **within the range of {depth_from} to {depth_to}**, return it as the most probable depth.
        - Image was sort with ascending with the depth, so the result should increase in each crop
        - If **multiple depth candidates** exist, prioritize the value that follows a common depth-related keyword (e.g., **HD, D, R, CIL**).
        - If a number appears alone (e.g., **CIL 0.9**), but no clear depth label exists, **still consider it if it matches the range**.
        - If a depth value is found **with surrounding words**, extract only the number (e.g., from 'HD: 120.6', return only `120.6`).
        - If no valid depth values exist and the text contains **only words**, return `'Not found'`.

        ### **Output Rules:**
        - Return **only** the depth value as a number (e.g., `5.5`, `10.43`, `1.2`).
        - Ensure **no extra characters** like `5.5\\n`, `1.2\\n`, or `120.6 HD`—return only the number.
        - Trim whitespace for the final result
        - Do not exaplain any thing, just numbers
    """)

    # Read image from uploaded file
    file_bytes = np.frombuffer(file.read(), np.uint8)
    image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)

    if image is None:
        return jsonify({'error': 'Invalid image file'}), 400

    try:
        # Step 1: Run segmentation
        _, result, detection_time = run_box_core_block_segment(image)

        # Step 2: Transform format and process segments
        segments = transform_format(result)
        merged_segments = merge_boxes_as_one_segment(segments)

        # Find merged box and filter segments inside
        merged_box = next(seg for seg in merged_segments if seg['class'].lower() == 'box')
        final_segments = filter_segments_inside_box(merged_segments, merged_box)

        # Step 3: Apply transformation if use_row is True
        warped_image = None
        transformed_seg = final_segments
        warped_image_url = None
        warped_image_filename = None

        if use_row:
            warped_image, transformed_seg = apply_box_transform_to_segments(
                filter_seg=final_segments,
                original_image=image,
                extent=10
            )

            # Upload warped image to Azure storage
            original_filename = file.filename
            if original_filename:
                name_without_ext = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                warped_filename = f"{name_without_ext}_warped.jpg"
            else:
                warped_filename = None

            upload_result = upload_image_to_azure(warped_image, filename=warped_filename)
            if upload_result["success"]:
                warped_image_url = upload_result["url"]
                warped_image_filename = upload_result["filename"]

        # Step 4: Crop blocks and run Google OCR and Gemini
        cropped_box_images = crop_boxes_from_segments(segments, image)

        google_result = run_google_vision_on_cropped_images(
            cropped_box_images,
            api_key=GOOGLE_VISION_API_KEY
        )

        gemini_result = run_gemini_prompt_on_cropped_images(
            cropped_box_images,
            prompt=custom_prompt,
            api_key=GEMINI_API_KEY
        )

        # Step 5: Filter only Block segments and add OCR/Gemini results
        block_segments = []
        block_index = 0

        for seg in transformed_seg:
            if seg['class'] == 'Block':
                block_data = {
                    "class": seg['class'],
                    "x": seg['x'],
                    "y": seg['y'],
                    "width": seg['width'],
                    "height": seg['height'],
                    "confidence": seg['confidence']
                }

                # Add corresponding Google OCR and Gemini results
                if block_index < len(google_result[1]):
                    block_data["google_text"] = google_result[1][block_index]
                else:
                    block_data["google_text"] = "No text"

                if block_index < len(gemini_result[1]):
                    block_data["gemini_text"] = gemini_result[1][block_index]
                else:
                    block_data["gemini_text"] = "Not found"

                block_segments.append(block_data)
                block_index += 1

        # Prepare response
        response_data = {
            "blocks": block_segments,
            "processing_info": {
                "detection_time": detection_time,
                "google_processing_time": google_result[2],
                "gemini_processing_time": gemini_result[2],
                "use_row_transformation": use_row,
                "depth_range": {"from": depth_from, "to": depth_to},
                "total_blocks": len(block_segments)
            }
        }

        # Add warped image info if available
        if warped_image_url:
            response_data["warped_image_url"] = warped_image_url
            response_data["warped_image_filename"] = warped_image_filename

        return jsonify(response_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "segment_core_outline_server"})

@app.route('/', methods=['GET'])
def home():
    """Home endpoint with API documentation"""
    return jsonify({
        "service": "Segment Core Outline Server",
        "version": "1.0.0",
        "description": "Complete segmentation pipeline with Google OCR and Gemini processing",
        "endpoints": {
            "/process_core_outline": {
                "method": "POST",
                "description": "Process image and return Block segments with integrated Google OCR and Gemini results",
                "parameters": {
                    "image": "Image file (required)",
                    "use_row": "Boolean to apply row transformation (optional, default: true)",
                    "depth_from": "Minimum depth value (optional, default: 0)",
                    "depth_to": "Maximum depth value (optional, default: 2.4)",
                    "custom_prompt": "Custom prompt for Gemini (optional)"
                },
                "returns": {
                    "blocks": "Array of Block objects with x, y, width, height, confidence, google_text, gemini_text",
                    "warped_image_url": "Azure URL of warped image (if use_row=true)",
                    "processing_info": "Metadata about processing times and parameters"
                }
            },
            "/health": {
                "method": "GET",
                "description": "Health check endpoint"
            }
        }
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8389, debug=True)
